from datetime import datetime
from config.database import db_manager

class Course:
    def __init__(self, id=None, name=None, description=None, total_levels=1,
                 sessions_per_level=10, fee_per_level=0, created_at=None, is_active=True):
        self.id = id
        self.name = name
        self.description = description
        self.total_levels = total_levels
        self.sessions_per_level = sessions_per_level
        self.fee_per_level = fee_per_level
        self.created_at = created_at
        self.is_active = is_active
    
    @classmethod
    def create(cls, name, description=None, total_levels=1, sessions_per_level=10, fee_per_level=0):
        """Create a new course"""
        new_id = db_manager.execute_update(
            '''INSERT INTO courses (name, description, total_levels, sessions_per_level, fee_per_level)
               VALUES (?, ?, ?, ?, ?)''',
            (name, description, total_levels, sessions_per_level, fee_per_level)
        )
        return cls.get_by_id(new_id)
    
    @classmethod
    def get_by_id(cls, course_id):
        """Get course by ID"""
        result = db_manager.execute_query(
            "SELECT * FROM courses WHERE id = ? AND is_active = 1", (course_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_all(cls):
        """Get all active courses"""
        results = db_manager.execute_query(
            "SELECT * FROM courses WHERE is_active = 1 ORDER BY name"
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def search(cls, search_term):
        """Search courses by name"""
        search_pattern = f"%{search_term}%"
        results = db_manager.execute_query(
            '''SELECT * FROM courses 
               WHERE name LIKE ? AND is_active = 1
               ORDER BY name''',
            (search_pattern,)
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def _from_row(cls, row):
        """Create Course instance from database row"""
        return cls(
            id=row[0], name=row[1], description=row[2], total_levels=row[3],
            sessions_per_level=row[4], fee_per_level=row[5], created_at=row[6], is_active=row[7]
        )
    
    def update(self, **kwargs):
        """Update course information"""
        valid_fields = ['name', 'description', 'total_levels', 'sessions_per_level', 'fee_per_level']
        
        update_fields = []
        update_values = []
        
        for field, value in kwargs.items():
            if field in valid_fields:
                update_fields.append(f"{field} = ?")
                update_values.append(value)
                setattr(self, field, value)
        
        if update_fields:
            update_values.append(self.id)
            query = f"UPDATE courses SET {', '.join(update_fields)} WHERE id = ?"
            db_manager.execute_update(query, update_values)
    
    def get_groups(self):
        """Get all groups for this course"""
        results = db_manager.execute_query(
            '''SELECT g.*, t.full_name as teacher_name
               FROM groups g
               LEFT JOIN teachers t ON g.teacher_id = t.id
               WHERE g.course_id = ? AND g.is_active = 1
               ORDER BY g.level, g.name''',
            (self.id,)
        )
        return results
    
    def get_groups_by_level(self, level):
        """Get groups for a specific level"""
        results = db_manager.execute_query(
            '''SELECT g.*, t.full_name as teacher_name
               FROM groups g
               LEFT JOIN teachers t ON g.teacher_id = t.id
               WHERE g.course_id = ? AND g.level = ? AND g.is_active = 1
               ORDER BY g.name''',
            (self.id, level)
        )
        return results
    
    def get_enrolled_students(self):
        """Get all students enrolled in this course"""
        results = db_manager.execute_query(
            '''SELECT s.*, se.enrollment_date, se.completion_date
               FROM students s
               JOIN student_enrollments se ON s.id = se.student_id
               WHERE se.course_id = ? AND se.is_active = 1 AND s.is_active = 1
               ORDER BY s.full_name''',
            (self.id,)
        )
        return results
    
    def get_students_count(self):
        """Get total number of enrolled students"""
        result = db_manager.execute_query(
            '''SELECT COUNT(*)
               FROM student_enrollments se
               JOIN students s ON se.student_id = s.id
               WHERE se.course_id = ? AND se.is_active = 1 AND s.is_active = 1''',
            (self.id,)
        )
        return result[0][0] if result else 0
    
    def get_revenue_stats(self):
        """Get revenue statistics for this course"""
        # Get total fees collected
        result = db_manager.execute_query(
            '''SELECT SUM(ft.amount)
               FROM financial_transactions ft
               JOIN student_enrollments se ON ft.student_id = se.student_id
               WHERE se.course_id = ? AND ft.transaction_type = 'income' 
               AND ft.category = 'course_fee' ''',
            (self.id,)
        )
        
        total_revenue = result[0][0] if result and result[0][0] else 0
        
        # Get expected revenue (enrolled students * fee per level * current level)
        enrolled_students = self.get_enrolled_students()
        expected_revenue = 0
        
        for student in enrolled_students:
            # Assuming students pay for each level they complete
            student_level = student[8] if len(student) > 8 else 1  # current_level
            expected_revenue += self.fee_per_level * student_level
        
        return {
            'total_revenue': total_revenue,
            'expected_revenue': expected_revenue,
            'collection_rate': (total_revenue / expected_revenue * 100) if expected_revenue > 0 else 0,
            'enrolled_students': len(enrolled_students)
        }
    
    def create_group(self, name, level=1, teacher_id=None, max_students=20):
        """Create a new group for this course"""
        from models.group import Group
        return Group.create(name, self.id, level, teacher_id, max_students)
    
    def deactivate(self):
        """Deactivate course and all its groups"""
        self.is_active = False
        db_manager.execute_update(
            "UPDATE courses SET is_active = 0 WHERE id = ?", (self.id,)
        )
        
        # Deactivate all groups in this course
        db_manager.execute_update(
            "UPDATE groups SET is_active = 0 WHERE course_id = ?", (self.id,)
        )
        
        # Deactivate all enrollments
        db_manager.execute_update(
            "UPDATE student_enrollments SET is_active = 0 WHERE course_id = ?", (self.id,)
        )
    
    def to_dict(self):
        """Convert course to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'total_levels': self.total_levels,
            'sessions_per_level': self.sessions_per_level,
            'fee_per_level': self.fee_per_level,
            'created_at': self.created_at,
            'is_active': self.is_active
        }
