import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
import json
from config.database import db_manager
from config.settings import app_settings

class BackupManager:
    def __init__(self):
        self.backup_dir = "data/backups"
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """Ensure backup directory exists"""
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, backup_name=None):
        """Create a complete backup of the database and settings"""
        try:
            if backup_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"academy_backup_{timestamp}"
            
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # Backup database
                db_path = db_manager.db_path
                if os.path.exists(db_path):
                    backup_zip.write(db_path, "academy.db")
                
                # Backup settings (export as JSON)
                settings_data = self.export_settings()
                backup_zip.writestr("settings.json", json.dumps(settings_data, indent=2))
                
                # Backup metadata
                metadata = {
                    "backup_date": datetime.now().isoformat(),
                    "academy_name": app_settings.get_academy_name(),
                    "version": "1.0.0",
                    "backup_type": "full"
                }
                backup_zip.writestr("metadata.json", json.dumps(metadata, indent=2))
                
                # Backup student photos directory if it exists
                photos_dir = "assets/student_photos"
                if os.path.exists(photos_dir):
                    for root, dirs, files in os.walk(photos_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_path = os.path.relpath(file_path, ".")
                            backup_zip.write(file_path, arc_path)
            
            return backup_path, "Backup created successfully"
        
        except Exception as e:
            return None, f"Backup failed: {str(e)}"
    
    def restore_backup(self, backup_path):
        """Restore from a backup file"""
        try:
            if not os.path.exists(backup_path):
                return False, "Backup file not found"
            
            # Create temporary directory for extraction
            temp_dir = "data/temp_restore"
            os.makedirs(temp_dir, exist_ok=True)
            
            try:
                with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                    backup_zip.extractall(temp_dir)
                
                # Verify backup integrity
                metadata_path = os.path.join(temp_dir, "metadata.json")
                if not os.path.exists(metadata_path):
                    return False, "Invalid backup file: missing metadata"
                
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                # Backup current database before restore
                current_backup_path, _ = self.create_backup("pre_restore_backup")
                
                # Restore database
                restored_db_path = os.path.join(temp_dir, "academy.db")
                if os.path.exists(restored_db_path):
                    shutil.copy2(restored_db_path, db_manager.db_path)
                
                # Restore settings
                settings_path = os.path.join(temp_dir, "settings.json")
                if os.path.exists(settings_path):
                    self.import_settings(settings_path)
                
                # Restore student photos
                photos_backup_dir = os.path.join(temp_dir, "assets", "student_photos")
                if os.path.exists(photos_backup_dir):
                    photos_dir = "assets/student_photos"
                    os.makedirs(photos_dir, exist_ok=True)
                    
                    for root, dirs, files in os.walk(photos_backup_dir):
                        for file in files:
                            src_path = os.path.join(root, file)
                            rel_path = os.path.relpath(src_path, photos_backup_dir)
                            dst_path = os.path.join(photos_dir, rel_path)
                            
                            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                            shutil.copy2(src_path, dst_path)
                
                return True, f"Backup restored successfully from {metadata['backup_date']}"
            
            finally:
                # Clean up temporary directory
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
        
        except Exception as e:
            return False, f"Restore failed: {str(e)}"
    
    def list_backups(self):
        """List all available backups"""
        backups = []
        
        try:
            if not os.path.exists(self.backup_dir):
                return backups
            
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.zip'):
                    backup_path = os.path.join(self.backup_dir, filename)
                    
                    try:
                        # Get backup metadata
                        with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                            if 'metadata.json' in backup_zip.namelist():
                                metadata_content = backup_zip.read('metadata.json')
                                metadata = json.loads(metadata_content.decode('utf-8'))
                            else:
                                # Fallback for backups without metadata
                                metadata = {
                                    "backup_date": datetime.fromtimestamp(
                                        os.path.getmtime(backup_path)
                                    ).isoformat(),
                                    "academy_name": "Unknown",
                                    "version": "Unknown",
                                    "backup_type": "unknown"
                                }
                        
                        backup_info = {
                            "filename": filename,
                            "path": backup_path,
                            "size": os.path.getsize(backup_path),
                            "metadata": metadata
                        }
                        
                        backups.append(backup_info)
                    
                    except Exception as e:
                        print(f"Error reading backup {filename}: {e}")
                        continue
            
            # Sort by backup date (newest first)
            backups.sort(key=lambda x: x['metadata']['backup_date'], reverse=True)
        
        except Exception as e:
            print(f"Error listing backups: {e}")
        
        return backups
    
    def delete_backup(self, backup_path):
        """Delete a backup file"""
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                return True, "Backup deleted successfully"
            else:
                return False, "Backup file not found"
        except Exception as e:
            return False, f"Failed to delete backup: {str(e)}"
    
    def export_settings(self):
        """Export current settings to dictionary"""
        try:
            settings_data = {}
            
            # Get all settings from database
            results = db_manager.execute_query("SELECT key, value FROM settings")
            
            for key, value in results:
                settings_data[key] = value
            
            return settings_data
        
        except Exception as e:
            print(f"Error exporting settings: {e}")
            return {}
    
    def import_settings(self, settings_file_path):
        """Import settings from JSON file"""
        try:
            with open(settings_file_path, 'r') as f:
                settings_data = json.load(f)
            
            # Import each setting
            for key, value in settings_data.items():
                app_settings.set(key, value)
            
            return True, "Settings imported successfully"
        
        except Exception as e:
            return False, f"Failed to import settings: {str(e)}"
    
    def create_automatic_backup(self):
        """Create automatic backup based on settings"""
        try:
            backup_frequency = app_settings.get('backup_frequency', 'weekly')
            
            # Check if automatic backup is needed
            last_backup_date = app_settings.get('last_automatic_backup')
            
            if last_backup_date:
                last_backup = datetime.fromisoformat(last_backup_date)
                now = datetime.now()
                
                days_since_backup = (now - last_backup).days
                
                # Determine if backup is needed based on frequency
                backup_needed = False
                if backup_frequency == 'daily' and days_since_backup >= 1:
                    backup_needed = True
                elif backup_frequency == 'weekly' and days_since_backup >= 7:
                    backup_needed = True
                elif backup_frequency == 'monthly' and days_since_backup >= 30:
                    backup_needed = True
                
                if not backup_needed:
                    return None, "Automatic backup not needed yet"
            
            # Create automatic backup
            backup_path, message = self.create_backup("automatic_backup")
            
            if backup_path:
                # Update last backup date
                app_settings.set('last_automatic_backup', datetime.now().isoformat())
                
                # Clean up old automatic backups (keep last 5)
                self.cleanup_automatic_backups()
            
            return backup_path, message
        
        except Exception as e:
            return None, f"Automatic backup failed: {str(e)}"
    
    def cleanup_automatic_backups(self, keep_count=5):
        """Clean up old automatic backups"""
        try:
            backups = self.list_backups()
            automatic_backups = [
                b for b in backups 
                if b['filename'].startswith('automatic_backup_')
            ]
            
            # Sort by date and keep only the most recent ones
            automatic_backups.sort(
                key=lambda x: x['metadata']['backup_date'], 
                reverse=True
            )
            
            # Delete old backups
            for backup in automatic_backups[keep_count:]:
                self.delete_backup(backup['path'])
        
        except Exception as e:
            print(f"Error cleaning up automatic backups: {e}")
    
    def verify_backup(self, backup_path):
        """Verify backup integrity"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                # Check if required files exist
                required_files = ['academy.db', 'metadata.json']
                
                for required_file in required_files:
                    if required_file not in backup_zip.namelist():
                        return False, f"Missing required file: {required_file}"
                
                # Test database integrity
                temp_db_path = "data/temp_verify.db"
                try:
                    backup_zip.extract('academy.db', 'data/')
                    os.rename('data/academy.db', temp_db_path)
                    
                    # Try to connect and query the database
                    conn = sqlite3.connect(temp_db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    conn.close()
                    
                    if len(tables) == 0:
                        return False, "Database appears to be empty"
                    
                    return True, "Backup verification successful"
                
                finally:
                    if os.path.exists(temp_db_path):
                        os.remove(temp_db_path)
        
        except Exception as e:
            return False, f"Backup verification failed: {str(e)}"
    
    def get_backup_size_mb(self, backup_path):
        """Get backup file size in MB"""
        try:
            size_bytes = os.path.getsize(backup_path)
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0
