#!/usr/bin/env python3
"""
Educational Academy Management System
A comprehensive desktop application for managing educational academies.

Author: Augment Agent
Version: 1.0.0
"""

import sys
import os
import customtkinter as ctk
from PIL import Image
import tkinter.messagebox as messagebox

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import application modules
from config.database import db_manager
from config.settings import app_settings
from views.login_window import LoginWindow

class AcademyManagementApp:
    def __init__(self):
        """Initialize the Academy Management Application"""
        self.setup_customtkinter()
        self.current_user = None
        self.main_window = None
        
        # Initialize database
        try:
            db_manager.init_database()
            print("Database initialized successfully")
        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to initialize database: {e}")
            sys.exit(1)
        
        # Create login window
        self.login_window = LoginWindow(self)
    
    def setup_customtkinter(self):
        """Setup CustomTkinter appearance and theme"""
        # Set appearance mode and color theme
        ctk.set_appearance_mode(app_settings.get_theme_mode())
        ctk.set_default_color_theme("blue")  # Can be customized later
        
        # Configure window scaling
        ctk.set_widget_scaling(1.0)
        ctk.set_window_scaling(1.0)
    
    def login_success(self, user):
        """Handle successful login"""
        self.current_user = user
        print(f"User {user.username} logged in successfully")
        
        # Close login window
        self.login_window.destroy()
        
        # Open main application window
        self.open_main_window()
    
    def open_main_window(self):
        """Open the main application window"""
        from views.main_window import MainWindow
        self.main_window = MainWindow(self, self.current_user)
    
    def logout(self):
        """Handle user logout"""
        if self.main_window:
            self.main_window.destroy()
            self.main_window = None
        
        self.current_user = None
        
        # Reopen login window
        self.login_window = LoginWindow(self)
    
    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            if self.main_window:
                self.main_window.destroy()
            if self.login_window:
                self.login_window.destroy()
            sys.exit(0)
    
    def run(self):
        """Start the application"""
        try:
            self.login_window.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            self.exit_application()
        except Exception as e:
            messagebox.showerror("Application Error", f"An unexpected error occurred: {e}")
            print(f"Application error: {e}")
            sys.exit(1)

def main():
    """Main entry point"""
    print("Starting Educational Academy Management System...")
    
    # Create and run the application
    app = AcademyManagementApp()
    app.run()

if __name__ == "__main__":
    main()
