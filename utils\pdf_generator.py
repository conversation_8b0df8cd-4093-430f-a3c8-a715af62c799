from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
from config.settings import app_settings

class PDFGenerator:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles"""
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            alignment=TA_CENTER
        )
        
        self.header_style = ParagraphStyle(
            'CustomHeader',
            parent=self.styles['Heading3'],
            fontSize=12,
            spaceAfter=10,
            textColor=colors.darkblue
        )
    
    def create_header(self, doc_title):
        """Create document header"""
        elements = []
        
        # Academy name
        academy_name = app_settings.get_academy_name()
        elements.append(Paragraph(academy_name, self.title_style))
        
        # Document title
        elements.append(Paragraph(doc_title, self.subtitle_style))
        
        # Date
        date_str = datetime.now().strftime("%B %d, %Y")
        elements.append(Paragraph(f"Generated on: {date_str}", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def generate_student_report(self, student, output_path):
        """Generate individual student report"""
        doc = SimpleDocTemplate(output_path, pagesize=letter)
        elements = []
        
        # Header
        elements.extend(self.create_header("Student Report"))
        
        # Student basic information
        elements.append(Paragraph("Student Information", self.header_style))
        
        student_data = [
            ["Student ID:", student.student_id],
            ["Full Name:", student.full_name],
            ["Parent Phone:", student.parent_phone or "N/A"],
            ["Address:", student.address or "N/A"],
            ["Current Level:", str(student.current_level)],
            ["Enrollment Date:", str(student.enrollment_date) if student.enrollment_date else "N/A"]
        ]
        
        student_table = Table(student_data, colWidths=[2*inch, 4*inch])
        student_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey)
        ]))
        
        elements.append(student_table)
        elements.append(Spacer(1, 20))
        
        # Attendance summary
        attendance_summary = student.get_attendance_summary()
        if attendance_summary['total_sessions'] > 0:
            elements.append(Paragraph("Attendance Summary", self.header_style))
            
            attendance_data = [
                ["Total Sessions:", str(attendance_summary['total_sessions'])],
                ["Present Sessions:", str(attendance_summary['present_sessions'])],
                ["Absent Sessions:", str(attendance_summary['absent_sessions'])],
                ["Attendance Rate:", f"{attendance_summary['attendance_rate']:.1f}%"]
            ]
            
            attendance_table = Table(attendance_data, colWidths=[2*inch, 2*inch])
            attendance_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey)
            ]))
            
            elements.append(attendance_table)
        
        # Build PDF
        doc.build(elements)
        return output_path
    
    def generate_students_list(self, students, output_path):
        """Generate students list report"""
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        elements = []
        
        # Header
        elements.extend(self.create_header("Students List"))
        
        # Summary
        elements.append(Paragraph(f"Total Students: {len(students)}", self.header_style))
        elements.append(Spacer(1, 10))
        
        # Students table
        table_data = [["#", "Student ID", "Full Name", "Group", "Level", "Parent Phone"]]
        
        for i, student in enumerate(students, 1):
            # Get group name
            group_name = "No Group"
            if student.current_group_id:
                from models.group import Group
                group = Group.get_by_id(student.current_group_id)
                if group:
                    group_name = group.name
            
            table_data.append([
                str(i),
                student.student_id,
                student.full_name,
                group_name,
                str(student.current_level),
                student.parent_phone or "N/A"
            ])
        
        # Create table
        students_table = Table(table_data, colWidths=[0.5*inch, 1.2*inch, 2*inch, 1.5*inch, 0.8*inch, 1.5*inch])
        students_table.setStyle(TableStyle([
            # Header row
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            
            # Data rows
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        elements.append(students_table)
        
        # Build PDF
        doc.build(elements)
        return output_path
    
    def generate_attendance_report(self, group, attendance_data, start_date, end_date, output_path):
        """Generate attendance report for a group"""
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        elements = []
        
        # Header
        elements.extend(self.create_header("Attendance Report"))
        
        # Group information
        elements.append(Paragraph(f"Group: {group.name}", self.header_style))
        elements.append(Paragraph(f"Period: {start_date} to {end_date}", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        
        # Attendance table
        if attendance_data:
            # Group attendance by date
            dates = sorted(set(record[3] for record in attendance_data))  # session_date
            students = sorted(set((record[8], record[9]) for record in attendance_data))  # student_name, student_code
            
            # Create table header
            table_data = [["Student ID", "Student Name"] + [date.strftime("%m/%d") for date in dates]]
            
            # Create attendance grid
            for student_code, student_name in students:
                row = [student_code, student_name]
                
                for date in dates:
                    # Find attendance for this student on this date
                    present = False
                    for record in attendance_data:
                        if record[8] == student_name and record[3] == date:
                            present = record[5]  # is_present
                            break
                    
                    row.append("✓" if present else "✗")
                
                table_data.append(row)
            
            # Create table
            col_widths = [1*inch, 2*inch] + [0.5*inch] * len(dates)
            attendance_table = Table(table_data, colWidths=col_widths)
            
            # Style the table
            style_commands = [
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]
            
            # Color code attendance
            for row_idx in range(1, len(table_data)):
                for col_idx in range(2, len(table_data[0])):
                    if table_data[row_idx][col_idx] == "✓":
                        style_commands.append(('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.lightgreen))
                    else:
                        style_commands.append(('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.lightcoral))
            
            attendance_table.setStyle(TableStyle(style_commands))
            elements.append(attendance_table)
        else:
            elements.append(Paragraph("No attendance data found for the specified period.", self.styles['Normal']))
        
        # Build PDF
        doc.build(elements)
        return output_path
    
    def generate_financial_report(self, transactions, start_date, end_date, output_path):
        """Generate financial report"""
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        elements = []
        
        # Header
        elements.extend(self.create_header("Financial Report"))
        
        # Period
        elements.append(Paragraph(f"Period: {start_date} to {end_date}", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        
        # Summary calculations
        total_income = sum(t[3] for t in transactions if t[1] == 'income')  # amount
        total_expenses = sum(t[3] for t in transactions if t[1] == 'expense')
        profit_loss = total_income - total_expenses
        
        # Summary table
        summary_data = [
            ["Total Income:", f"${total_income:.2f}"],
            ["Total Expenses:", f"${total_expenses:.2f}"],
            ["Profit/Loss:", f"${profit_loss:.2f}"]
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 2), (-1, 2), colors.lightblue if profit_loss >= 0 else colors.lightcoral)
        ]))
        
        elements.append(summary_table)
        elements.append(Spacer(1, 20))
        
        # Detailed transactions
        elements.append(Paragraph("Transaction Details", self.header_style))
        
        if transactions:
            table_data = [["Date", "Type", "Category", "Amount", "Description"]]
            
            for transaction in transactions:
                table_data.append([
                    str(transaction[7]),  # transaction_date
                    transaction[1].title(),  # transaction_type
                    transaction[2],  # category
                    f"${transaction[3]:.2f}",  # amount
                    transaction[4] or "N/A"  # description
                ])
            
            transactions_table = Table(table_data, colWidths=[1*inch, 1*inch, 1.5*inch, 1*inch, 2.5*inch])
            transactions_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
            ]))
            
            elements.append(transactions_table)
        else:
            elements.append(Paragraph("No transactions found for the specified period.", self.styles['Normal']))
        
        # Build PDF
        doc.build(elements)
        return output_path
    
    def ensure_output_directory(self, output_path):
        """Ensure output directory exists"""
        directory = os.path.dirname(output_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
        return output_path
