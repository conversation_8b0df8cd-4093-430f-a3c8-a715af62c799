from datetime import datetime, date
from config.database import db_manager

class FinancialTransaction:
    def __init__(self, id=None, transaction_type=None, category=None, amount=0,
                 description=None, student_id=None, teacher_id=None,
                 transaction_date=None, created_at=None):
        self.id = id
        self.transaction_type = transaction_type  # 'income' or 'expense'
        self.category = category
        self.amount = amount
        self.description = description
        self.student_id = student_id
        self.teacher_id = teacher_id
        self.transaction_date = transaction_date
        self.created_at = created_at
    
    @classmethod
    def create(cls, transaction_type, category, amount, description=None,
               student_id=None, teacher_id=None, transaction_date=None):
        """Create a new financial transaction"""
        if transaction_date is None:
            transaction_date = date.today()
        
        new_id = db_manager.execute_update(
            '''INSERT INTO financial_transactions 
               (transaction_type, category, amount, description, student_id, teacher_id, transaction_date)
               VALUES (?, ?, ?, ?, ?, ?, ?)''',
            (transaction_type, category, amount, description, student_id, teacher_id, transaction_date)
        )
        return cls.get_by_id(new_id)
    
    @classmethod
    def get_by_id(cls, transaction_id):
        """Get transaction by ID"""
        result = db_manager.execute_query(
            "SELECT * FROM financial_transactions WHERE id = ?", (transaction_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_all(cls, start_date=None, end_date=None, transaction_type=None):
        """Get all transactions with optional filters"""
        base_query = '''
            SELECT ft.*, s.full_name as student_name, t.full_name as teacher_name
            FROM financial_transactions ft
            LEFT JOIN students s ON ft.student_id = s.id
            LEFT JOIN teachers t ON ft.teacher_id = t.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            base_query += " AND ft.transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND ft.transaction_date <= ?"
            params.append(end_date)
        
        if transaction_type:
            base_query += " AND ft.transaction_type = ?"
            params.append(transaction_type)
        
        base_query += " ORDER BY ft.transaction_date DESC, ft.created_at DESC"
        
        results = db_manager.execute_query(base_query, params)
        return results
    
    @classmethod
    def get_by_student(cls, student_id, start_date=None, end_date=None):
        """Get transactions for a specific student"""
        base_query = '''
            SELECT * FROM financial_transactions
            WHERE student_id = ?
        '''
        params = [student_id]
        
        if start_date:
            base_query += " AND transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND transaction_date <= ?"
            params.append(end_date)
        
        base_query += " ORDER BY transaction_date DESC"
        
        results = db_manager.execute_query(base_query, params)
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def get_by_teacher(cls, teacher_id, start_date=None, end_date=None):
        """Get transactions for a specific teacher"""
        base_query = '''
            SELECT * FROM financial_transactions
            WHERE teacher_id = ?
        '''
        params = [teacher_id]
        
        if start_date:
            base_query += " AND transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND transaction_date <= ?"
            params.append(end_date)
        
        base_query += " ORDER BY transaction_date DESC"
        
        results = db_manager.execute_query(base_query, params)
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def get_monthly_summary(cls, year, month):
        """Get monthly financial summary"""
        results = db_manager.execute_query(
            '''SELECT 
                   transaction_type,
                   category,
                   SUM(amount) as total_amount,
                   COUNT(*) as transaction_count
               FROM financial_transactions
               WHERE strftime('%Y', transaction_date) = ? 
               AND strftime('%m', transaction_date) = ?
               GROUP BY transaction_type, category
               ORDER BY transaction_type, category''',
            (str(year), f"{month:02d}")
        )
        return results
    
    @classmethod
    def get_yearly_summary(cls, year):
        """Get yearly financial summary"""
        results = db_manager.execute_query(
            '''SELECT 
                   strftime('%m', transaction_date) as month,
                   transaction_type,
                   SUM(amount) as total_amount
               FROM financial_transactions
               WHERE strftime('%Y', transaction_date) = ?
               GROUP BY strftime('%m', transaction_date), transaction_type
               ORDER BY month''',
            (str(year),)
        )
        return results
    
    @classmethod
    def get_profit_loss(cls, start_date=None, end_date=None):
        """Calculate profit/loss for a period"""
        base_query = '''
            SELECT 
                transaction_type,
                SUM(amount) as total
            FROM financial_transactions
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            base_query += " AND transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND transaction_date <= ?"
            params.append(end_date)
        
        base_query += " GROUP BY transaction_type"
        
        results = db_manager.execute_query(base_query, params)
        
        income = 0
        expenses = 0
        
        for row in results:
            if row[0] == 'income':
                income = row[1]
            elif row[0] == 'expense':
                expenses = row[1]
        
        return {
            'income': income,
            'expenses': expenses,
            'profit_loss': income - expenses
        }
    
    @classmethod
    def get_category_summary(cls, transaction_type, start_date=None, end_date=None):
        """Get summary by category for a transaction type"""
        base_query = '''
            SELECT 
                category,
                SUM(amount) as total_amount,
                COUNT(*) as transaction_count,
                AVG(amount) as average_amount
            FROM financial_transactions
            WHERE transaction_type = ?
        '''
        params = [transaction_type]
        
        if start_date:
            base_query += " AND transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND transaction_date <= ?"
            params.append(end_date)
        
        base_query += " GROUP BY category ORDER BY total_amount DESC"
        
        results = db_manager.execute_query(base_query, params)
        return results
    
    @classmethod
    def record_course_payment(cls, student_id, amount, course_name, description=None):
        """Record a course fee payment"""
        if description is None:
            description = f"Course fee payment for {course_name}"
        
        return cls.create(
            transaction_type='income',
            category='course_fee',
            amount=amount,
            description=description,
            student_id=student_id
        )
    
    @classmethod
    def record_salary_payment(cls, teacher_id, amount, description=None):
        """Record a salary payment"""
        if description is None:
            description = "Salary payment"
        
        return cls.create(
            transaction_type='expense',
            category='salary',
            amount=amount,
            description=description,
            teacher_id=teacher_id
        )
    
    @classmethod
    def record_expense(cls, category, amount, description):
        """Record a general expense"""
        return cls.create(
            transaction_type='expense',
            category=category,
            amount=amount,
            description=description
        )
    
    @classmethod
    def _from_row(cls, row):
        """Create FinancialTransaction instance from database row"""
        return cls(
            id=row[0], transaction_type=row[1], category=row[2], amount=row[3],
            description=row[4], student_id=row[5], teacher_id=row[6],
            transaction_date=row[7], created_at=row[8]
        )
    
    def update(self, **kwargs):
        """Update transaction information"""
        valid_fields = ['category', 'amount', 'description', 'transaction_date']
        
        update_fields = []
        update_values = []
        
        for field, value in kwargs.items():
            if field in valid_fields:
                update_fields.append(f"{field} = ?")
                update_values.append(value)
                setattr(self, field, value)
        
        if update_fields:
            update_values.append(self.id)
            query = f"UPDATE financial_transactions SET {', '.join(update_fields)} WHERE id = ?"
            db_manager.execute_update(query, update_values)
    
    def delete(self):
        """Delete transaction"""
        db_manager.execute_update(
            "DELETE FROM financial_transactions WHERE id = ?", (self.id,)
        )
    
    def to_dict(self):
        """Convert transaction to dictionary"""
        return {
            'id': self.id,
            'transaction_type': self.transaction_type,
            'category': self.category,
            'amount': self.amount,
            'description': self.description,
            'student_id': self.student_id,
            'teacher_id': self.teacher_id,
            'transaction_date': self.transaction_date,
            'created_at': self.created_at
        }
