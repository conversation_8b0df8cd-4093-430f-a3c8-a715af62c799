from datetime import datetime, date
from config.database import db_manager

class Student:
    def __init__(self, id=None, student_id=None, full_name=None, parent_phone=None,
                 address=None, profile_photo_path=None, current_group_id=None,
                 current_level=1, enrollment_date=None, created_at=None, is_active=True):
        self.id = id
        self.student_id = student_id
        self.full_name = full_name
        self.parent_phone = parent_phone
        self.address = address
        self.profile_photo_path = profile_photo_path
        self.current_group_id = current_group_id
        self.current_level = current_level
        self.enrollment_date = enrollment_date
        self.created_at = created_at
        self.is_active = is_active
    
    @classmethod
    def create(cls, student_id, full_name, parent_phone=None, address=None,
               profile_photo_path=None, current_group_id=None):
        """Create a new student"""
        new_id = db_manager.execute_update(
            '''INSERT INTO students (student_id, full_name, parent_phone, address,
                                   profile_photo_path, current_group_id)
               VALUES (?, ?, ?, ?, ?, ?)''',
            (student_id, full_name, parent_phone, address, profile_photo_path, current_group_id)
        )
        return cls.get_by_id(new_id)
    
    @classmethod
    def get_by_id(cls, student_id):
        """Get student by database ID"""
        result = db_manager.execute_query(
            "SELECT * FROM students WHERE id = ? AND is_active = 1", (student_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_by_student_id(cls, student_id):
        """Get student by student ID"""
        result = db_manager.execute_query(
            "SELECT * FROM students WHERE student_id = ? AND is_active = 1", (student_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_all(cls):
        """Get all active students"""
        results = db_manager.execute_query(
            "SELECT * FROM students WHERE is_active = 1 ORDER BY full_name"
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def get_by_group(cls, group_id):
        """Get students in a specific group"""
        results = db_manager.execute_query(
            "SELECT * FROM students WHERE current_group_id = ? AND is_active = 1 ORDER BY full_name",
            (group_id,)
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def search(cls, search_term):
        """Search students by name or student ID"""
        search_pattern = f"%{search_term}%"
        results = db_manager.execute_query(
            '''SELECT * FROM students 
               WHERE (full_name LIKE ? OR student_id LIKE ?) AND is_active = 1
               ORDER BY full_name''',
            (search_pattern, search_pattern)
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def _from_row(cls, row):
        """Create Student instance from database row"""
        return cls(
            id=row[0], student_id=row[1], full_name=row[2], parent_phone=row[3],
            address=row[4], profile_photo_path=row[5], current_group_id=row[6],
            current_level=row[7], enrollment_date=row[8], created_at=row[9], is_active=row[10]
        )
    
    def update(self, **kwargs):
        """Update student information"""
        valid_fields = ['student_id', 'full_name', 'parent_phone', 'address',
                       'profile_photo_path', 'current_group_id', 'current_level']
        
        update_fields = []
        update_values = []
        
        for field, value in kwargs.items():
            if field in valid_fields:
                update_fields.append(f"{field} = ?")
                update_values.append(value)
                setattr(self, field, value)
        
        if update_fields:
            update_values.append(self.id)
            query = f"UPDATE students SET {', '.join(update_fields)} WHERE id = ?"
            db_manager.execute_update(query, update_values)
    
    def transfer_to_group(self, new_group_id, new_level=None):
        """Transfer student to a new group"""
        if new_level is None:
            new_level = self.current_level
        
        self.update(current_group_id=new_group_id, current_level=new_level)
    
    def enroll_in_course(self, course_id):
        """Enroll student in a course"""
        db_manager.execute_update(
            '''INSERT OR IGNORE INTO student_enrollments (student_id, course_id)
               VALUES (?, ?)''',
            (self.id, course_id)
        )
    
    def get_enrollments(self):
        """Get student's course enrollments"""
        results = db_manager.execute_query(
            '''SELECT se.*, c.name as course_name
               FROM student_enrollments se
               JOIN courses c ON se.course_id = c.id
               WHERE se.student_id = ? AND se.is_active = 1''',
            (self.id,)
        )
        return results
    
    def get_attendance_summary(self, group_id=None):
        """Get attendance summary for student"""
        if group_id:
            results = db_manager.execute_query(
                '''SELECT COUNT(*) as total_sessions,
                          SUM(CASE WHEN is_present = 1 THEN 1 ELSE 0 END) as present_sessions
                   FROM attendance
                   WHERE student_id = ? AND group_id = ?''',
                (self.id, group_id)
            )
        else:
            results = db_manager.execute_query(
                '''SELECT COUNT(*) as total_sessions,
                          SUM(CASE WHEN is_present = 1 THEN 1 ELSE 0 END) as present_sessions
                   FROM attendance
                   WHERE student_id = ?''',
                (self.id,)
            )
        
        if results and results[0][0] > 0:
            total, present = results[0]
            return {
                'total_sessions': total,
                'present_sessions': present,
                'absent_sessions': total - present,
                'attendance_rate': (present / total) * 100 if total > 0 else 0
            }
        return {'total_sessions': 0, 'present_sessions': 0, 'absent_sessions': 0, 'attendance_rate': 0}
    
    def add_note(self, note_type, content, created_by):
        """Add a note for the student"""
        db_manager.execute_update(
            '''INSERT INTO student_notes (student_id, note_type, content, created_by)
               VALUES (?, ?, ?, ?)''',
            (self.id, note_type, content, created_by)
        )
    
    def get_notes(self):
        """Get all notes for the student"""
        results = db_manager.execute_query(
            '''SELECT sn.*, u.full_name as created_by_name
               FROM student_notes sn
               JOIN users u ON sn.created_by = u.id
               WHERE sn.student_id = ?
               ORDER BY sn.created_at DESC''',
            (self.id,)
        )
        return results
    
    def deactivate(self):
        """Deactivate student"""
        self.is_active = False
        db_manager.execute_update(
            "UPDATE students SET is_active = 0 WHERE id = ?", (self.id,)
        )
    
    def to_dict(self):
        """Convert student to dictionary"""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'full_name': self.full_name,
            'parent_phone': self.parent_phone,
            'address': self.address,
            'profile_photo_path': self.profile_photo_path,
            'current_group_id': self.current_group_id,
            'current_level': self.current_level,
            'enrollment_date': self.enrollment_date,
            'created_at': self.created_at,
            'is_active': self.is_active
        }
