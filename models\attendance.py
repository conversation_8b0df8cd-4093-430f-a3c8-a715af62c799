from datetime import datetime, date
from config.database import db_manager

class Attendance:
    def __init__(self, id=None, student_id=None, group_id=None, session_date=None,
                 session_number=None, is_present=False, notes=None, recorded_at=None):
        self.id = id
        self.student_id = student_id
        self.group_id = group_id
        self.session_date = session_date
        self.session_number = session_number
        self.is_present = is_present
        self.notes = notes
        self.recorded_at = recorded_at
    
    @classmethod
    def record_attendance(cls, student_id, group_id, session_date, session_number, is_present=True, notes=None):
        """Record attendance for a student"""
        attendance_id = db_manager.execute_update(
            '''INSERT OR REPLACE INTO attendance 
               (student_id, group_id, session_date, session_number, is_present, notes)
               VALUES (?, ?, ?, ?, ?, ?)''',
            (student_id, group_id, session_date, session_number, is_present, notes)
        )
        return cls.get_by_id(attendance_id)
    
    @classmethod
    def get_by_id(cls, attendance_id):
        """Get attendance record by ID"""
        result = db_manager.execute_query(
            "SELECT * FROM attendance WHERE id = ?", (attendance_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_by_session(cls, group_id, session_date):
        """Get all attendance records for a specific session"""
        results = db_manager.execute_query(
            '''SELECT a.*, s.full_name as student_name, s.student_id as student_code
               FROM attendance a
               JOIN students s ON a.student_id = s.id
               WHERE a.group_id = ? AND a.session_date = ?
               ORDER BY s.full_name''',
            (group_id, session_date)
        )
        return results
    
    @classmethod
    def get_student_attendance(cls, student_id, group_id=None, start_date=None, end_date=None):
        """Get attendance records for a specific student"""
        base_query = '''
            SELECT a.*, g.name as group_name, c.name as course_name
            FROM attendance a
            JOIN groups g ON a.group_id = g.id
            JOIN courses c ON g.course_id = c.id
            WHERE a.student_id = ?
        '''
        params = [student_id]
        
        if group_id:
            base_query += " AND a.group_id = ?"
            params.append(group_id)
        
        if start_date:
            base_query += " AND a.session_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND a.session_date <= ?"
            params.append(end_date)
        
        base_query += " ORDER BY a.session_date DESC"
        
        results = db_manager.execute_query(base_query, params)
        return results
    
    @classmethod
    def get_group_attendance(cls, group_id, start_date=None, end_date=None):
        """Get attendance records for a specific group"""
        base_query = '''
            SELECT a.*, s.full_name as student_name, s.student_id as student_code
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.group_id = ?
        '''
        params = [group_id]
        
        if start_date:
            base_query += " AND a.session_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND a.session_date <= ?"
            params.append(end_date)
        
        base_query += " ORDER BY a.session_date DESC, s.full_name"
        
        results = db_manager.execute_query(base_query, params)
        return results
    
    @classmethod
    def mark_present_by_student_code(cls, student_code, group_id, session_date, session_number):
        """Mark student present using their student ID code"""
        # First, get the student's database ID
        student_result = db_manager.execute_query(
            "SELECT id FROM students WHERE student_id = ? AND is_active = 1",
            (student_code,)
        )
        
        if not student_result:
            return None, "Student not found"
        
        student_id = student_result[0][0]
        
        # Check if student is in the specified group
        group_check = db_manager.execute_query(
            "SELECT id FROM students WHERE id = ? AND current_group_id = ?",
            (student_id, group_id)
        )
        
        if not group_check:
            return None, "Student is not in this group"
        
        # Record attendance
        attendance = cls.record_attendance(student_id, group_id, session_date, session_number, True)
        return attendance, "Attendance recorded successfully"
    
    @classmethod
    def create_session_for_group(cls, group_id, session_date, session_number):
        """Create attendance records for all students in a group for a new session"""
        # Get all students in the group
        students = db_manager.execute_query(
            "SELECT id FROM students WHERE current_group_id = ? AND is_active = 1",
            (group_id,)
        )
        
        created_records = []
        for student in students:
            student_id = student[0]
            # Create attendance record with default absent status
            attendance_id = db_manager.execute_update(
                '''INSERT OR IGNORE INTO attendance 
                   (student_id, group_id, session_date, session_number, is_present)
                   VALUES (?, ?, ?, ?, 0)''',
                (student_id, group_id, session_date, session_number)
            )
            if attendance_id:
                created_records.append(attendance_id)
        
        return created_records
    
    @classmethod
    def get_attendance_summary_by_student(cls, student_id, group_id=None):
        """Get attendance summary for a student"""
        base_query = '''
            SELECT 
                COUNT(*) as total_sessions,
                SUM(CASE WHEN is_present = 1 THEN 1 ELSE 0 END) as present_sessions
            FROM attendance
            WHERE student_id = ?
        '''
        params = [student_id]
        
        if group_id:
            base_query += " AND group_id = ?"
            params.append(group_id)
        
        result = db_manager.execute_query(base_query, params)
        
        if result and result[0][0] > 0:
            total, present = result[0]
            return {
                'total_sessions': total,
                'present_sessions': present,
                'absent_sessions': total - present,
                'attendance_rate': (present / total) * 100 if total > 0 else 0
            }
        
        return {
            'total_sessions': 0,
            'present_sessions': 0,
            'absent_sessions': 0,
            'attendance_rate': 0
        }
    
    @classmethod
    def get_absent_students(cls, group_id, session_date):
        """Get students who were absent on a specific date"""
        results = db_manager.execute_query(
            '''SELECT s.*, a.session_number
               FROM students s
               JOIN attendance a ON s.id = a.student_id
               WHERE a.group_id = ? AND a.session_date = ? AND a.is_present = 0
               ORDER BY s.full_name''',
            (group_id, session_date)
        )
        return results
    
    @classmethod
    def get_frequent_absentees(cls, group_id, absence_threshold=3):
        """Get students with frequent absences in a group"""
        results = db_manager.execute_query(
            '''SELECT s.*, COUNT(*) as absence_count
               FROM students s
               JOIN attendance a ON s.id = a.student_id
               WHERE a.group_id = ? AND a.is_present = 0
               GROUP BY s.id
               HAVING absence_count >= ?
               ORDER BY absence_count DESC''',
            (group_id, absence_threshold)
        )
        return results
    
    @classmethod
    def _from_row(cls, row):
        """Create Attendance instance from database row"""
        return cls(
            id=row[0], student_id=row[1], group_id=row[2], session_date=row[3],
            session_number=row[4], is_present=row[5], notes=row[6], recorded_at=row[7]
        )
    
    def update(self, is_present=None, notes=None):
        """Update attendance record"""
        update_fields = []
        update_values = []
        
        if is_present is not None:
            update_fields.append("is_present = ?")
            update_values.append(is_present)
            self.is_present = is_present
        
        if notes is not None:
            update_fields.append("notes = ?")
            update_values.append(notes)
            self.notes = notes
        
        if update_fields:
            update_values.append(self.id)
            query = f"UPDATE attendance SET {', '.join(update_fields)} WHERE id = ?"
            db_manager.execute_update(query, update_values)
    
    def to_dict(self):
        """Convert attendance to dictionary"""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'group_id': self.group_id,
            'session_date': self.session_date,
            'session_number': self.session_number,
            'is_present': self.is_present,
            'notes': self.notes,
            'recorded_at': self.recorded_at
        }
