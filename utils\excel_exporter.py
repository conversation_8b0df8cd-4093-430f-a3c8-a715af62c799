import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from datetime import datetime
from config.settings import app_settings

class ExcelExporter:
    def __init__(self):
        self.setup_styles()
    
    def setup_styles(self):
        """Setup Excel styles"""
        # Header style
        self.header_font = Font(bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        self.header_alignment = Alignment(horizontal="center", vertical="center")
        
        # Title style
        self.title_font = Font(bold=True, size=16, color="366092")
        self.title_alignment = Alignment(horizontal="center", vertical="center")
        
        # Border style
        thin_border = Side(border_style="thin", color="000000")
        self.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
        
        # Alternating row colors
        self.light_fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
        self.white_fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
    
    def create_workbook_header(self, worksheet, title, start_row=1):
        """Create a header for the worksheet"""
        # Academy name
        worksheet.cell(row=start_row, column=1, value=app_settings.get_academy_name())
        worksheet.cell(row=start_row, column=1).font = self.title_font
        worksheet.cell(row=start_row, column=1).alignment = self.title_alignment
        
        # Report title
        worksheet.cell(row=start_row + 1, column=1, value=title)
        worksheet.cell(row=start_row + 1, column=1).font = Font(bold=True, size=14)
        worksheet.cell(row=start_row + 1, column=1).alignment = self.title_alignment
        
        # Date
        date_str = datetime.now().strftime("%B %d, %Y at %I:%M %p")
        worksheet.cell(row=start_row + 2, column=1, value=f"Generated on: {date_str}")
        worksheet.cell(row=start_row + 2, column=1).font = Font(size=10)
        worksheet.cell(row=start_row + 2, column=1).alignment = self.title_alignment
        
        return start_row + 4  # Return next available row
    
    def apply_table_style(self, worksheet, start_row, end_row, start_col, end_col):
        """Apply table styling to a range"""
        # Apply borders to all cells
        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                worksheet.cell(row=row, column=col).border = self.border
        
        # Style header row
        for col in range(start_col, end_col + 1):
            cell = worksheet.cell(row=start_row, column=col)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.header_alignment
        
        # Apply alternating row colors
        for row in range(start_row + 1, end_row + 1):
            fill = self.light_fill if (row - start_row) % 2 == 0 else self.white_fill
            for col in range(start_col, end_col + 1):
                worksheet.cell(row=row, column=col).fill = fill
    
    def auto_adjust_columns(self, worksheet):
        """Auto-adjust column widths"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def export_students_list(self, students, output_path):
        """Export students list to Excel"""
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Students List"
        
        # Create header
        current_row = self.create_workbook_header(worksheet, "Students List")
        
        # Summary
        worksheet.cell(row=current_row, column=1, value=f"Total Students: {len(students)}")
        worksheet.cell(row=current_row, column=1).font = Font(bold=True)
        current_row += 2
        
        # Table headers
        headers = ["#", "Student ID", "Full Name", "Parent Phone", "Address", "Current Group", "Level", "Enrollment Date"]
        start_row = current_row
        
        for col, header in enumerate(headers, 1):
            worksheet.cell(row=current_row, column=col, value=header)
        
        current_row += 1
        
        # Student data
        for i, student in enumerate(students, 1):
            # Get group name
            group_name = "No Group"
            if student.current_group_id:
                from models.group import Group
                group = Group.get_by_id(student.current_group_id)
                if group:
                    group_name = group.name
            
            row_data = [
                i,
                student.student_id,
                student.full_name,
                student.parent_phone or "N/A",
                student.address or "N/A",
                group_name,
                student.current_level,
                str(student.enrollment_date) if student.enrollment_date else "N/A"
            ]
            
            for col, value in enumerate(row_data, 1):
                worksheet.cell(row=current_row, column=col, value=value)
            
            current_row += 1
        
        # Apply styling
        self.apply_table_style(worksheet, start_row, current_row - 1, 1, len(headers))
        self.auto_adjust_columns(worksheet)
        
        # Save workbook
        workbook.save(output_path)
        return output_path
    
    def export_attendance_report(self, group, attendance_data, start_date, end_date, output_path):
        """Export attendance report to Excel"""
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Attendance Report"
        
        # Create header
        current_row = self.create_workbook_header(worksheet, "Attendance Report")
        
        # Group and period info
        worksheet.cell(row=current_row, column=1, value=f"Group: {group.name}")
        worksheet.cell(row=current_row, column=1).font = Font(bold=True)
        current_row += 1
        
        worksheet.cell(row=current_row, column=1, value=f"Period: {start_date} to {end_date}")
        worksheet.cell(row=current_row, column=1).font = Font(bold=True)
        current_row += 2
        
        if attendance_data:
            # Group attendance by date and student
            dates = sorted(set(record[3] for record in attendance_data))  # session_date
            students = sorted(set((record[8], record[9]) for record in attendance_data))  # student_name, student_code
            
            # Create table headers
            headers = ["Student ID", "Student Name"] + [date.strftime("%m/%d/%Y") for date in dates] + ["Total Present", "Total Absent", "Attendance %"]
            start_row = current_row
            
            for col, header in enumerate(headers, 1):
                worksheet.cell(row=current_row, column=col, value=header)
            
            current_row += 1
            
            # Student attendance data
            for student_code, student_name in students:
                row_data = [student_code, student_name]
                present_count = 0
                total_sessions = 0
                
                # Attendance for each date
                for date in dates:
                    present = False
                    for record in attendance_data:
                        if record[8] == student_name and record[3] == date:
                            present = record[5]  # is_present
                            total_sessions += 1
                            if present:
                                present_count += 1
                            break
                    
                    row_data.append("Present" if present else "Absent")
                
                # Summary columns
                absent_count = total_sessions - present_count
                attendance_rate = (present_count / total_sessions * 100) if total_sessions > 0 else 0
                
                row_data.extend([present_count, absent_count, f"{attendance_rate:.1f}%"])
                
                for col, value in enumerate(row_data, 1):
                    cell = worksheet.cell(row=current_row, column=col, value=value)
                    
                    # Color code attendance
                    if col > 2 and col <= len(dates) + 2:  # Attendance columns
                        if value == "Present":
                            cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
                        elif value == "Absent":
                            cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")
                
                current_row += 1
            
            # Apply styling
            self.apply_table_style(worksheet, start_row, current_row - 1, 1, len(headers))
        else:
            worksheet.cell(row=current_row, column=1, value="No attendance data found for the specified period.")
        
        self.auto_adjust_columns(worksheet)
        
        # Save workbook
        workbook.save(output_path)
        return output_path
    
    def export_financial_report(self, transactions, start_date, end_date, output_path):
        """Export financial report to Excel"""
        workbook = openpyxl.Workbook()
        
        # Summary sheet
        summary_sheet = workbook.active
        summary_sheet.title = "Financial Summary"
        
        current_row = self.create_workbook_header(summary_sheet, "Financial Summary")
        
        # Period
        summary_sheet.cell(row=current_row, column=1, value=f"Period: {start_date} to {end_date}")
        summary_sheet.cell(row=current_row, column=1).font = Font(bold=True)
        current_row += 2
        
        # Calculate totals
        total_income = sum(t[3] for t in transactions if t[1] == 'income')
        total_expenses = sum(t[3] for t in transactions if t[1] == 'expense')
        profit_loss = total_income - total_expenses
        
        # Summary table
        summary_data = [
            ["Category", "Amount"],
            ["Total Income", f"${total_income:.2f}"],
            ["Total Expenses", f"${total_expenses:.2f}"],
            ["Profit/Loss", f"${profit_loss:.2f}"]
        ]
        
        start_row = current_row
        for row_idx, row_data in enumerate(summary_data):
            for col_idx, value in enumerate(row_data, 1):
                cell = summary_sheet.cell(row=current_row + row_idx, column=col_idx, value=value)
                
                # Highlight profit/loss row
                if row_idx == 3:  # Profit/Loss row
                    if profit_loss >= 0:
                        cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
                    else:
                        cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")
        
        self.apply_table_style(summary_sheet, start_row, start_row + len(summary_data) - 1, 1, 2)
        
        # Detailed transactions sheet
        if transactions:
            detail_sheet = workbook.create_sheet("Transaction Details")
            current_row = self.create_workbook_header(detail_sheet, "Transaction Details")
            
            # Table headers
            headers = ["Date", "Type", "Category", "Amount", "Description", "Student/Teacher"]
            start_row = current_row
            
            for col, header in enumerate(headers, 1):
                detail_sheet.cell(row=current_row, column=col, value=header)
            
            current_row += 1
            
            # Transaction data
            for transaction in transactions:
                # Get related person name
                person_name = "N/A"
                if len(transaction) > 10:  # Has student_name
                    person_name = transaction[10] or "N/A"
                elif len(transaction) > 11:  # Has teacher_name
                    person_name = transaction[11] or "N/A"
                
                row_data = [
                    str(transaction[7]),  # transaction_date
                    transaction[1].title(),  # transaction_type
                    transaction[2],  # category
                    f"${transaction[3]:.2f}",  # amount
                    transaction[4] or "N/A",  # description
                    person_name
                ]
                
                for col, value in enumerate(row_data, 1):
                    detail_sheet.cell(row=current_row, column=col, value=value)
                
                current_row += 1
            
            # Apply styling
            self.apply_table_style(detail_sheet, start_row, current_row - 1, 1, len(headers))
            self.auto_adjust_columns(detail_sheet)
        
        self.auto_adjust_columns(summary_sheet)
        
        # Save workbook
        workbook.save(output_path)
        return output_path
    
    def export_teachers_list(self, teachers, output_path):
        """Export teachers list to Excel"""
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Teachers List"
        
        # Create header
        current_row = self.create_workbook_header(worksheet, "Teachers List")
        
        # Summary
        worksheet.cell(row=current_row, column=1, value=f"Total Teachers: {len(teachers)}")
        worksheet.cell(row=current_row, column=1).font = Font(bold=True)
        current_row += 2
        
        # Table headers
        headers = ["#", "Teacher ID", "Full Name", "Specialization", "Phone", "Email", "Salary"]
        start_row = current_row
        
        for col, header in enumerate(headers, 1):
            worksheet.cell(row=current_row, column=col, value=header)
        
        current_row += 1
        
        # Teacher data
        for i, teacher in enumerate(teachers, 1):
            row_data = [
                i,
                teacher.teacher_id,
                teacher.full_name,
                teacher.specialization or "N/A",
                teacher.phone or "N/A",
                teacher.email or "N/A",
                f"${teacher.salary:.2f}" if teacher.salary else "$0.00"
            ]
            
            for col, value in enumerate(row_data, 1):
                worksheet.cell(row=current_row, column=col, value=value)
            
            current_row += 1
        
        # Apply styling
        self.apply_table_style(worksheet, start_row, current_row - 1, 1, len(headers))
        self.auto_adjust_columns(worksheet)
        
        # Save workbook
        workbook.save(output_path)
        return output_path
