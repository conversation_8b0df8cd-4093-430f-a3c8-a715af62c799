from datetime import datetime
from config.database import db_manager

class Teacher:
    def __init__(self, id=None, teacher_id=None, full_name=None, specialization=None,
                 phone=None, email=None, address=None, salary=0, created_at=None, is_active=True):
        self.id = id
        self.teacher_id = teacher_id
        self.full_name = full_name
        self.specialization = specialization
        self.phone = phone
        self.email = email
        self.address = address
        self.salary = salary
        self.created_at = created_at
        self.is_active = is_active
    
    @classmethod
    def create(cls, teacher_id, full_name, specialization=None, phone=None,
               email=None, address=None, salary=0):
        """Create a new teacher"""
        new_id = db_manager.execute_update(
            '''INSERT INTO teachers (teacher_id, full_name, specialization, phone, email, address, salary)
               VALUES (?, ?, ?, ?, ?, ?, ?)''',
            (teacher_id, full_name, specialization, phone, email, address, salary)
        )
        return cls.get_by_id(new_id)
    
    @classmethod
    def get_by_id(cls, teacher_id):
        """Get teacher by database ID"""
        result = db_manager.execute_query(
            "SELECT * FROM teachers WHERE id = ? AND is_active = 1", (teacher_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_by_teacher_id(cls, teacher_id):
        """Get teacher by teacher ID"""
        result = db_manager.execute_query(
            "SELECT * FROM teachers WHERE teacher_id = ? AND is_active = 1", (teacher_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_all(cls):
        """Get all active teachers"""
        results = db_manager.execute_query(
            "SELECT * FROM teachers WHERE is_active = 1 ORDER BY full_name"
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def search(cls, search_term):
        """Search teachers by name or teacher ID"""
        search_pattern = f"%{search_term}%"
        results = db_manager.execute_query(
            '''SELECT * FROM teachers 
               WHERE (full_name LIKE ? OR teacher_id LIKE ?) AND is_active = 1
               ORDER BY full_name''',
            (search_pattern, search_pattern)
        )
        return [cls._from_row(row) for row in results]
    
    @classmethod
    def _from_row(cls, row):
        """Create Teacher instance from database row"""
        return cls(
            id=row[0], teacher_id=row[1], full_name=row[2], specialization=row[3],
            phone=row[4], email=row[5], address=row[6], salary=row[7],
            created_at=row[8], is_active=row[9]
        )
    
    def update(self, **kwargs):
        """Update teacher information"""
        valid_fields = ['teacher_id', 'full_name', 'specialization', 'phone',
                       'email', 'address', 'salary']
        
        update_fields = []
        update_values = []
        
        for field, value in kwargs.items():
            if field in valid_fields:
                update_fields.append(f"{field} = ?")
                update_values.append(value)
                setattr(self, field, value)
        
        if update_fields:
            update_values.append(self.id)
            query = f"UPDATE teachers SET {', '.join(update_fields)} WHERE id = ?"
            db_manager.execute_update(query, update_values)
    
    def get_assigned_groups(self):
        """Get groups assigned to this teacher"""
        results = db_manager.execute_query(
            '''SELECT g.*, c.name as course_name
               FROM groups g
               JOIN courses c ON g.course_id = c.id
               WHERE g.teacher_id = ? AND g.is_active = 1
               ORDER BY c.name, g.level''',
            (self.id,)
        )
        return results
    
    def get_students_count(self):
        """Get total number of students taught by this teacher"""
        result = db_manager.execute_query(
            '''SELECT COUNT(DISTINCT s.id)
               FROM students s
               JOIN groups g ON s.current_group_id = g.id
               WHERE g.teacher_id = ? AND s.is_active = 1 AND g.is_active = 1''',
            (self.id,)
        )
        return result[0][0] if result else 0
    
    def get_monthly_salary_payments(self, year=None, month=None):
        """Get salary payments for a specific month"""
        if year is None:
            year = datetime.now().year
        if month is None:
            month = datetime.now().month
        
        results = db_manager.execute_query(
            '''SELECT * FROM financial_transactions
               WHERE teacher_id = ? AND transaction_type = 'expense' 
               AND category = 'salary'
               AND strftime('%Y', transaction_date) = ?
               AND strftime('%m', transaction_date) = ?
               ORDER BY transaction_date DESC''',
            (self.id, str(year), f"{month:02d}")
        )
        return results
    
    def record_salary_payment(self, amount, description=None, transaction_date=None):
        """Record a salary payment for this teacher"""
        if transaction_date is None:
            transaction_date = datetime.now().date()
        
        if description is None:
            description = f"Salary payment for {self.full_name}"
        
        db_manager.execute_update(
            '''INSERT INTO financial_transactions 
               (transaction_type, category, amount, description, teacher_id, transaction_date)
               VALUES (?, ?, ?, ?, ?, ?)''',
            ('expense', 'salary', amount, description, self.id, transaction_date)
        )
    
    def get_performance_stats(self):
        """Get performance statistics for this teacher"""
        # Get attendance rate for teacher's groups
        attendance_result = db_manager.execute_query(
            '''SELECT 
                   COUNT(*) as total_sessions,
                   SUM(CASE WHEN a.is_present = 1 THEN 1 ELSE 0 END) as present_sessions
               FROM attendance a
               JOIN groups g ON a.group_id = g.id
               WHERE g.teacher_id = ?''',
            (self.id,)
        )
        
        # Get number of active groups
        groups_result = db_manager.execute_query(
            "SELECT COUNT(*) FROM groups WHERE teacher_id = ? AND is_active = 1",
            (self.id,)
        )
        
        stats = {
            'total_groups': groups_result[0][0] if groups_result else 0,
            'total_students': self.get_students_count(),
            'attendance_rate': 0
        }
        
        if attendance_result and attendance_result[0][0] > 0:
            total, present = attendance_result[0]
            stats['attendance_rate'] = (present / total) * 100 if total > 0 else 0
        
        return stats
    
    def deactivate(self):
        """Deactivate teacher"""
        self.is_active = False
        db_manager.execute_update(
            "UPDATE teachers SET is_active = 0 WHERE id = ?", (self.id,)
        )
        
        # Unassign from all groups
        db_manager.execute_update(
            "UPDATE groups SET teacher_id = NULL WHERE teacher_id = ?", (self.id,)
        )
    
    def to_dict(self):
        """Convert teacher to dictionary"""
        return {
            'id': self.id,
            'teacher_id': self.teacher_id,
            'full_name': self.full_name,
            'specialization': self.specialization,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'salary': self.salary,
            'created_at': self.created_at,
            'is_active': self.is_active
        }
