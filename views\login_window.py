import customtkinter as ctk
import tkinter.messagebox as messagebox
from models.user import User
from config.settings import app_settings

class LoginWindow(ctk.CTk):
    def __init__(self, app):
        super().__init__()

        self.app = app
        self.setup_window()
        self.create_widgets()

        # Center the window
        self.center_window()

        # Focus on username entry
        self.username_entry.focus()

    def setup_window(self):
        """Setup the login window"""
        self.title(f"{app_settings.get_academy_name()} - Login")
        self.geometry("400x500")
        self.resizable(False, False)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Academy logo/title
        title_label = ctk.CTkLabel(
            main_frame,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(30, 10))

        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Management System",
            font=ctk.CTkFont(size=16)
        )
        subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 30))

        # Login form frame
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        form_frame.grid_columnconfigure(0, weight=1)

        # Username field
        username_label = ctk.CTkLabel(form_frame, text="Username:")
        username_label.grid(row=0, column=0, padx=20, pady=(20, 5), sticky="w")

        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your username",
            width=300
        )
        self.username_entry.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="ew")
        self.username_entry.bind("<Return>", lambda e: self.password_entry.focus())

        # Password field
        password_label = ctk.CTkLabel(form_frame, text="Password:")
        password_label.grid(row=2, column=0, padx=20, pady=(0, 5), sticky="w")

        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your password",
            show="*",
            width=300
        )
        self.password_entry.grid(row=3, column=0, padx=20, pady=(0, 20), sticky="ew")
        self.password_entry.bind("<Return>", lambda e: self.login())

        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text="Login",
            command=self.login,
            width=300,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.login_button.grid(row=4, column=0, padx=20, pady=(0, 20))

        # Status label
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.grid(row=3, column=0, padx=20, pady=10)

        # Default credentials info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.grid(row=4, column=0, padx=20, pady=(10, 20), sticky="ew")

        info_label = ctk.CTkLabel(
            info_frame,
            text="Default Login Credentials:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        info_label.grid(row=0, column=0, padx=20, pady=(15, 5))

        creds_label = ctk.CTkLabel(
            info_frame,
            text="Username: admin\nPassword: admin123",
            font=ctk.CTkFont(size=11)
        )
        creds_label.grid(row=1, column=0, padx=20, pady=(0, 15))

        # Version info
        version_label = ctk.CTkLabel(
            main_frame,
            text="Version 1.0.0",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        version_label.grid(row=5, column=0, padx=20, pady=(0, 20))

    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()

        # Get window dimensions
        window_width = self.winfo_width()
        window_height = self.winfo_height()

        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # Calculate position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def login(self):
        """Handle login attempt"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get()

            # Validate input
            if not username:
                self.show_status("Please enter a username", "error")
                self.username_entry.focus()
                return

            if not password:
                self.show_status("Please enter a password", "error")
                self.password_entry.focus()
                return

            # Disable login button during authentication
            self.login_button.configure(state="disabled", text="Logging in...")
            self.show_status("Authenticating...", "info")

            # Update the UI
            self.update_idletasks()

            # Authenticate user
            user = User.get_by_username(username)

            if user and user.verify_password(password):
                self.show_status("Login successful!", "success")
                # Use after to delay the login success to avoid widget destruction issues
                self.after(100, lambda: self.app.login_success(user))
            else:
                self.show_status("Invalid username or password", "error")
                self.password_entry.delete(0, 'end')
                self.password_entry.focus()
                self.login_button.configure(state="normal", text="Login")

        except Exception as e:
            self.show_status(f"Login error: {str(e)}", "error")
            print(f"Login error: {e}")
            try:
                self.login_button.configure(state="normal", text="Login")
            except:
                pass

    def show_status(self, message, status_type="info"):
        """Show status message with appropriate color"""
        try:
            colors = {
                "info": "gray",
                "success": "green",
                "error": "red"
            }

            self.status_label.configure(
                text=message,
                text_color=colors.get(status_type, "gray")
            )

            # Clear status after 5 seconds for non-error messages
            if status_type != "error":
                self.after(5000, self.clear_status)
        except Exception as e:
            print(f"Error showing status: {e}")

    def clear_status(self):
        """Clear status message safely"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.configure(text="")
        except:
            pass

    def on_closing(self):
        """Handle window closing"""
        self.app.exit_application()

    def destroy(self):
        """Override destroy to handle cleanup"""
        super().destroy()
