#!/usr/bin/env python3
"""
Simple test version to identify and fix GUI issues
"""

import sys
import os
import customtkinter as ctk
import tkinter.messagebox as messagebox

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from config.settings import app_settings
from models.user import User

class SimpleLoginWindow(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        self.setup_window()
        self.create_widgets()
        self.center_window()
    
    def setup_window(self):
        """Setup the login window"""
        self.title(f"{app_settings.get_academy_name()} - Login")
        self.geometry("400x300")
        self.resizable(False, False)
        
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="Academy Management System",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 30))
        
        # Username field
        username_label = ctk.CTkLabel(main_frame, text="Username:")
        username_label.grid(row=1, column=0, padx=20, pady=(0, 5), sticky="w")
        
        self.username_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="Enter username",
            width=300
        )
        self.username_entry.grid(row=2, column=0, padx=20, pady=(0, 15), sticky="ew")
        
        # Password field
        password_label = ctk.CTkLabel(main_frame, text="Password:")
        password_label.grid(row=3, column=0, padx=20, pady=(0, 5), sticky="w")
        
        self.password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="Enter password",
            show="*",
            width=300
        )
        self.password_entry.grid(row=4, column=0, padx=20, pady=(0, 20), sticky="ew")
        
        # Login button
        login_button = ctk.CTkButton(
            main_frame,
            text="Login",
            command=self.login,
            width=300,
            height=40
        )
        login_button.grid(row=5, column=0, padx=20, pady=(0, 20))
        
        # Status label
        self.status_label = ctk.CTkLabel(main_frame, text="")
        self.status_label.grid(row=6, column=0, padx=20, pady=10)
        
        # Default credentials
        info_label = ctk.CTkLabel(
            main_frame,
            text="Default: admin / admin123",
            font=ctk.CTkFont(size=10)
        )
        info_label.grid(row=7, column=0, padx=20, pady=(0, 20))
    
    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()
        
        # Get window dimensions
        window_width = self.winfo_width()
        window_height = self.winfo_height()
        
        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # Calculate position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def login(self):
        """Handle login attempt"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get()
            
            if not username or not password:
                self.show_status("Please enter username and password", "error")
                return
            
            # Authenticate user
            user = User.get_by_username(username)
            
            if user and user.verify_password(password):
                self.show_status("Login successful!", "success")
                self.after(1000, self.open_main_window)
            else:
                self.show_status("Invalid credentials", "error")
                self.password_entry.delete(0, 'end')
        
        except Exception as e:
            self.show_status(f"Login error: {str(e)}", "error")
            print(f"Login error: {e}")
    
    def show_status(self, message, status_type="info"):
        """Show status message"""
        colors = {
            "info": "gray",
            "success": "green", 
            "error": "red"
        }
        
        self.status_label.configure(
            text=message,
            text_color=colors.get(status_type, "gray")
        )
    
    def open_main_window(self):
        """Open main window"""
        try:
            self.withdraw()  # Hide login window
            main_window = SimpleMainWindow(self)
            main_window.mainloop()
        except Exception as e:
            print(f"Error opening main window: {e}")
            messagebox.showerror("Error", f"Failed to open main window: {e}")

class SimpleMainWindow(ctk.CTk):
    def __init__(self, login_window):
        super().__init__()
        
        self.login_window = login_window
        self.setup_window()
        self.create_widgets()
        
        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_window(self):
        """Setup the main window"""
        self.title(f"{app_settings.get_academy_name()} - Dashboard")
        self.geometry("800x600")
        
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create and arrange widgets"""
        # Sidebar
        sidebar = ctk.CTkFrame(self, width=200, corner_radius=0)
        sidebar.grid(row=0, column=0, sticky="nsew")
        sidebar.grid_rowconfigure(10, weight=1)
        
        # Academy name
        academy_label = ctk.CTkLabel(
            sidebar,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        academy_label.grid(row=0, column=0, padx=20, pady=20)
        
        # Navigation buttons
        dashboard_btn = ctk.CTkButton(
            sidebar,
            text="📊 Dashboard",
            command=self.load_dashboard,
            anchor="w",
            height=40
        )
        dashboard_btn.grid(row=1, column=0, padx=20, pady=5, sticky="ew")
        
        students_btn = ctk.CTkButton(
            sidebar,
            text="👥 Students",
            command=self.load_students,
            anchor="w",
            height=40
        )
        students_btn.grid(row=2, column=0, padx=20, pady=5, sticky="ew")
        
        # Logout button
        logout_btn = ctk.CTkButton(
            sidebar,
            text="🚪 Logout",
            command=self.logout,
            anchor="w",
            height=40,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray80", "gray20")
        )
        logout_btn.grid(row=11, column=0, padx=20, pady=20, sticky="ew")
        
        # Main content area
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # Load dashboard by default
        self.load_dashboard()
    
    def clear_main_frame(self):
        """Clear the main content area"""
        try:
            for widget in self.main_frame.winfo_children():
                widget.destroy()
        except Exception as e:
            print(f"Error clearing main frame: {e}")
    
    def load_dashboard(self):
        """Load the dashboard view"""
        self.clear_main_frame()
        
        # Dashboard content
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Dashboard",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")
        
        # Welcome message
        welcome_label = ctk.CTkLabel(
            self.main_frame,
            text="Welcome to the Academy Management System!",
            font=ctk.CTkFont(size=16)
        )
        welcome_label.grid(row=1, column=0, padx=20, pady=10, sticky="w")
        
        # Stats frame
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        
        try:
            from models.student import Student
            from models.teacher import Teacher
            from models.course import Course
            
            total_students = len(Student.get_all())
            total_teachers = len(Teacher.get_all())
            total_courses = len(Course.get_all())
            
            stats_text = f"""
            📊 Academy Statistics:
            
            👥 Total Students: {total_students}
            👨‍🏫 Total Teachers: {total_teachers}
            📚 Total Courses: {total_courses}
            """
            
            stats_label = ctk.CTkLabel(
                stats_frame,
                text=stats_text,
                font=ctk.CTkFont(size=14),
                justify="left"
            )
            stats_label.grid(row=0, column=0, padx=20, pady=20)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                stats_frame,
                text=f"Error loading statistics: {e}",
                text_color="red"
            )
            error_label.grid(row=0, column=0, padx=20, pady=20)
    
    def load_students(self):
        """Load the students view"""
        self.clear_main_frame()
        
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Student Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")
        
        info_label = ctk.CTkLabel(
            self.main_frame,
            text="Student management functionality is working!\nFull interface will be available in the complete version.",
            font=ctk.CTkFont(size=14)
        )
        info_label.grid(row=1, column=0, padx=20, pady=20)
    
    def logout(self):
        """Handle logout"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.destroy()
            self.login_window.deiconify()  # Show login window again
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit?"):
            self.destroy()
            if self.login_window:
                self.login_window.destroy()

def main():
    """Main entry point"""
    print("Starting Simple Academy Management System Test...")
    
    # Initialize database
    try:
        db_manager.init_database()
        print("✓ Database initialized successfully")
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return
    
    # Set appearance
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # Create and run the application
    try:
        app = SimpleLoginWindow()
        app.mainloop()
    except Exception as e:
        print(f"Application error: {e}")
        messagebox.showerror("Error", f"Application error: {e}")

if __name__ == "__main__":
    main()
