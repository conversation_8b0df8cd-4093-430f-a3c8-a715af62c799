#!/usr/bin/env python3
"""
Test script to verify database and models functionality
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from models.user import User
from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.financial import FinancialTransaction

def test_database_connection():
    """Test database connection and initialization"""
    print("Testing database connection...")
    try:
        db_manager.init_database()
        print("✓ Database initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return False

def test_user_model():
    """Test user model functionality"""
    print("\nTesting User model...")
    try:
        # Test getting default admin user
        admin = User.get_by_username("admin")
        if admin:
            print(f"✓ Default admin user found: {admin.full_name}")
            
            # Test password verification
            if admin.verify_password("admin123"):
                print("✓ Password verification works")
            else:
                print("✗ Password verification failed")
            
            # Test permissions
            if admin.has_permission('all'):
                print("✓ Admin permissions work")
            else:
                print("✗ Admin permissions failed")
        else:
            print("✗ Default admin user not found")
            return False
        
        # Test creating a new user
        test_user = User.create("testuser", "password123", "staff", "Test User")
        if test_user:
            print(f"✓ Created test user: {test_user.full_name}")
            
            # Clean up
            test_user.deactivate()
            print("✓ Test user deactivated")
        else:
            print("✗ Failed to create test user")
        
        return True
    except Exception as e:
        print(f"✗ User model test failed: {e}")
        return False

def test_course_model():
    """Test course model functionality"""
    print("\nTesting Course model...")
    try:
        # Create a test course
        course = Course.create(
            name="Mathematics",
            description="Basic mathematics course",
            total_levels=3,
            sessions_per_level=12,
            fee_per_level=100.0
        )
        
        if course:
            print(f"✓ Created course: {course.name}")
            
            # Test getting course
            retrieved_course = Course.get_by_id(course.id)
            if retrieved_course and retrieved_course.name == "Mathematics":
                print("✓ Course retrieval works")
            else:
                print("✗ Course retrieval failed")
            
            # Test updating course
            course.update(fee_per_level=120.0)
            if course.fee_per_level == 120.0:
                print("✓ Course update works")
            else:
                print("✗ Course update failed")
            
            return True
        else:
            print("✗ Failed to create course")
            return False
    except Exception as e:
        print(f"✗ Course model test failed: {e}")
        return False

def test_teacher_model():
    """Test teacher model functionality"""
    print("\nTesting Teacher model...")
    try:
        # Create a test teacher
        teacher = Teacher.create(
            teacher_id="T001",
            full_name="John Smith",
            specialization="Mathematics",
            phone="************",
            email="<EMAIL>",
            salary=3000.0
        )
        
        if teacher:
            print(f"✓ Created teacher: {teacher.full_name}")
            
            # Test getting teacher
            retrieved_teacher = Teacher.get_by_teacher_id("T001")
            if retrieved_teacher and retrieved_teacher.full_name == "John Smith":
                print("✓ Teacher retrieval works")
            else:
                print("✗ Teacher retrieval failed")
            
            return True
        else:
            print("✗ Failed to create teacher")
            return False
    except Exception as e:
        print(f"✗ Teacher model test failed: {e}")
        return False

def test_group_model():
    """Test group model functionality"""
    print("\nTesting Group model...")
    try:
        # Get the course we created earlier
        courses = Course.get_all()
        if not courses:
            print("✗ No courses found for group test")
            return False
        
        course = courses[0]
        
        # Get the teacher we created earlier
        teachers = Teacher.get_all()
        teacher_id = teachers[0].id if teachers else None
        
        # Create a test group
        group = Group.create(
            name="Math Group A",
            course_id=course.id,
            level=1,
            teacher_id=teacher_id,
            max_students=20
        )
        
        if group:
            print(f"✓ Created group: {group.name}")
            
            # Test getting group
            retrieved_group = Group.get_by_id(group.id)
            if retrieved_group and retrieved_group.name == "Math Group A":
                print("✓ Group retrieval works")
            else:
                print("✗ Group retrieval failed")
            
            return True
        else:
            print("✗ Failed to create group")
            return False
    except Exception as e:
        print(f"✗ Group model test failed: {e}")
        return False

def test_student_model():
    """Test student model functionality"""
    print("\nTesting Student model...")
    try:
        # Get a group for the student
        groups = Group.get_all()
        group_id = groups[0][0] if groups else None  # groups[0] is a tuple from get_all()
        
        # Create a test student
        student = Student.create(
            student_id="S001",
            full_name="Alice Johnson",
            parent_phone="************",
            address="123 Main St, City",
            current_group_id=group_id
        )
        
        if student:
            print(f"✓ Created student: {student.full_name}")
            
            # Test getting student
            retrieved_student = Student.get_by_student_id("S001")
            if retrieved_student and retrieved_student.full_name == "Alice Johnson":
                print("✓ Student retrieval works")
            else:
                print("✗ Student retrieval failed")
            
            # Test updating student
            student.update(parent_phone="************")
            if student.parent_phone == "************":
                print("✓ Student update works")
            else:
                print("✗ Student update failed")
            
            return True
        else:
            print("✗ Failed to create student")
            return False
    except Exception as e:
        print(f"✗ Student model test failed: {e}")
        return False

def test_financial_model():
    """Test financial model functionality"""
    print("\nTesting Financial model...")
    try:
        # Create a test income transaction
        income = FinancialTransaction.create(
            transaction_type="income",
            category="course_fee",
            amount=100.0,
            description="Course fee payment"
        )
        
        if income:
            print(f"✓ Created income transaction: ${income.amount}")
            
            # Create a test expense transaction
            expense = FinancialTransaction.create(
                transaction_type="expense",
                category="rent",
                amount=500.0,
                description="Monthly rent"
            )
            
            if expense:
                print(f"✓ Created expense transaction: ${expense.amount}")
                
                # Test profit/loss calculation
                profit_loss = FinancialTransaction.get_profit_loss()
                expected_profit = income.amount - expense.amount
                
                if abs(profit_loss['profit_loss'] - expected_profit) < 0.01:
                    print(f"✓ Profit/loss calculation works: ${profit_loss['profit_loss']}")
                else:
                    print(f"✗ Profit/loss calculation failed: expected ${expected_profit}, got ${profit_loss['profit_loss']}")
                
                return True
            else:
                print("✗ Failed to create expense transaction")
                return False
        else:
            print("✗ Failed to create income transaction")
            return False
    except Exception as e:
        print(f"✗ Financial model test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("Educational Academy Management System - Database Tests")
    print("=" * 50)
    
    tests = [
        test_database_connection,
        test_user_model,
        test_course_model,
        test_teacher_model,
        test_group_model,
        test_student_model,
        test_financial_model
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The database and models are working correctly.")
    else:
        print(f"⚠️  {total - passed} test(s) failed. Please check the errors above.")
    
    print("=" * 50)

if __name__ == "__main__":
    run_all_tests()
