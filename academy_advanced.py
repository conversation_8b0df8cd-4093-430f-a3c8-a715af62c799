#!/usr/bin/env python3
"""
Educational Academy Management System - Advanced Version
Complete student management with all CRUD operations
"""

import sys
import os
import customtkinter as ctk
import tkinter.messagebox as messagebox
from tkinter import ttk, filedialog
import tkinter as tk
from PIL import Image, ImageTk
import shutil
from datetime import datetime
import uuid

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from config.settings import app_settings
from models.user import User
from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.attendance import Attendance
from models.financial import FinancialTransaction

class AcademyApp:
    def __init__(self):
        """Initialize the Academy Management Application"""
        self.setup_customtkinter()
        self.current_user = None
        self.main_window = None
        self.login_window = None

        # Create directories for student photos
        self.create_directories()

        # Initialize database
        try:
            db_manager.init_database()
            print("✓ Database initialized successfully")
        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to initialize database: {e}")
            sys.exit(1)

        # Create login window
        self.show_login()

    def create_directories(self):
        """Create necessary directories for the application"""
        directories = [
            "student_photos",
            "student_cards",
            "backups",
            "reports"
        ]

        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✓ Created directory: {directory}")

    def setup_customtkinter(self):
        """Setup CustomTkinter appearance and theme"""
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        ctk.set_widget_scaling(1.0)
        ctk.set_window_scaling(1.0)

    def show_login(self):
        """Show login window"""
        self.login_window = LoginWindow(self)

    def login_success(self, user):
        """Handle successful login"""
        try:
            self.current_user = user
            print(f"✓ User {user.username} logged in successfully")

            # Close login window safely
            if self.login_window:
                self.login_window.withdraw()
                self.login_window.quit()
                self.login_window = None

            # Open main application window
            self.show_main_window()
        except Exception as e:
            print(f"Error during login success: {e}")
            messagebox.showerror("Error", f"Failed to open main window: {e}")

    def show_main_window(self):
        """Show main application window"""
        try:
            self.main_window = MainWindow(self, self.current_user)
            self.main_window.mainloop()
        except Exception as e:
            print(f"Error showing main window: {e}")
            messagebox.showerror("Error", f"Main window error: {e}")

    def logout(self):
        """Handle user logout"""
        try:
            if self.main_window:
                self.main_window.quit()
                self.main_window = None

            self.current_user = None
            self.show_login()
        except Exception as e:
            print(f"Error during logout: {e}")
            self.exit_application()

    def exit_application(self):
        """Exit the application"""
        try:
            if self.main_window:
                self.main_window.quit()
            if self.login_window:
                self.login_window.quit()
            sys.exit(0)
        except:
            sys.exit(0)

    def run(self):
        """Start the application"""
        try:
            if self.login_window:
                self.login_window.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            self.exit_application()
        except Exception as e:
            print(f"Application error: {e}")
            self.exit_application()

class LoginWindow(ctk.CTk):
    def __init__(self, app):
        super().__init__()

        self.app = app
        self.setup_window()
        self.create_widgets()
        self.center_window()

        # Focus on username entry
        self.after(100, lambda: self.username_entry.focus())

    def setup_window(self):
        """Setup the login window"""
        self.title(f"{app_settings.get_academy_name()} - Advanced Login")
        self.geometry("500x650")
        self.resizable(False, False)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame with gradient-like effect
        main_frame = ctk.CTkFrame(self, corner_radius=15)
        main_frame.grid(row=0, column=0, padx=25, pady=25, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Header section
        header_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        header_frame.grid(row=0, column=0, padx=30, pady=(30, 20), sticky="ew")

        # Academy logo/title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎓 " + app_settings.get_academy_name(),
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#1f538d", "#4a9eff")
        )
        title_label.grid(row=0, column=0, pady=(0, 10))

        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Advanced Management System",
            font=ctk.CTkFont(size=18),
            text_color=("gray60", "gray40")
        )
        subtitle_label.grid(row=1, column=0)

        # Login form frame
        form_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        form_frame.grid(row=1, column=0, padx=30, pady=20, sticky="ew")
        form_frame.grid_columnconfigure(0, weight=1)

        # Username field
        username_label = ctk.CTkLabel(
            form_frame,
            text="👤 Username:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        username_label.grid(row=0, column=0, padx=25, pady=(25, 8), sticky="w")

        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your username",
            width=400,
            height=40,
            font=ctk.CTkFont(size=14),
            corner_radius=8
        )
        self.username_entry.grid(row=1, column=0, padx=25, pady=(0, 20), sticky="ew")
        self.username_entry.bind("<Return>", self.focus_password)

        # Password field
        password_label = ctk.CTkLabel(
            form_frame,
            text="🔒 Password:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        password_label.grid(row=2, column=0, padx=25, pady=(0, 8), sticky="w")

        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your password",
            show="*",
            width=400,
            height=40,
            font=ctk.CTkFont(size=14),
            corner_radius=8
        )
        self.password_entry.grid(row=3, column=0, padx=25, pady=(0, 25), sticky="ew")
        self.password_entry.bind("<Return>", self.login_enter)

        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text="🚀 Login",
            command=self.login,
            width=400,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=8,
            fg_color=("#1f538d", "#4a9eff"),
            hover_color=("#14375e", "#3a7dd8")
        )
        self.login_button.grid(row=4, column=0, padx=25, pady=(0, 25))

        # Quick login section
        quick_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        quick_frame.grid(row=2, column=0, padx=30, pady=(0, 20), sticky="ew")
        quick_frame.grid_columnconfigure((0, 1, 2), weight=1)

        quick_label = ctk.CTkLabel(
            quick_frame,
            text="⚡ Quick Login:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        quick_label.grid(row=0, column=0, columnspan=3, padx=20, pady=(20, 10))

        # Quick login buttons with better styling
        admin_btn = ctk.CTkButton(
            quick_frame,
            text="👑 Admin",
            command=lambda: self.quick_login("admin", "admin123"),
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#dc2626", "#ef4444"),
            hover_color=("#b91c1c", "#dc2626")
        )
        admin_btn.grid(row=1, column=0, padx=8, pady=(0, 20))

        staff_btn = ctk.CTkButton(
            quick_frame,
            text="👨‍💼 Staff",
            command=lambda: self.quick_login("staff1", "password123"),
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#059669", "#10b981"),
            hover_color=("#047857", "#059669")
        )
        staff_btn.grid(row=1, column=1, padx=8, pady=(0, 20))

        accountant_btn = ctk.CTkButton(
            quick_frame,
            text="💰 Accountant",
            command=lambda: self.quick_login("accountant1", "password123"),
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#7c3aed", "#8b5cf6"),
            hover_color=("#6d28d9", "#7c3aed")
        )
        accountant_btn.grid(row=1, column=2, padx=8, pady=(0, 20))

        # Status label
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=13),
            height=25
        )
        self.status_label.grid(row=3, column=0, padx=30, pady=(0, 10))

        # Info section
        info_frame = ctk.CTkFrame(main_frame, corner_radius=10, fg_color=("gray90", "gray20"))
        info_frame.grid(row=4, column=0, padx=30, pady=(0, 30), sticky="ew")

        info_label = ctk.CTkLabel(
            info_frame,
            text="🆕 New Features: Complete Student Management!",
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=("#059669", "#10b981")
        )
        info_label.grid(row=0, column=0, padx=20, pady=(15, 8))

        features_text = """✅ Add/Edit/Delete Students  ✅ Upload Photos  ✅ Print ID Cards
👑 Admin: admin / admin123  👨‍💼 Staff: staff1 / password123  💰 Accountant: accountant1 / password123"""

        features_label = ctk.CTkLabel(
            info_frame,
            text=features_text,
            font=ctk.CTkFont(size=10),
            justify="center"
        )
        features_label.grid(row=1, column=0, padx=20, pady=(0, 15))

    def quick_login(self, username, password):
        """Quick login with predefined credentials"""
        self.username_entry.delete(0, 'end')
        self.password_entry.delete(0, 'end')
        self.username_entry.insert(0, username)
        self.password_entry.insert(0, password)
        self.login()

    def focus_password(self, event=None):
        """Focus on password field"""
        self.password_entry.focus()

    def login_enter(self, event=None):
        """Handle Enter key in password field"""
        self.login()

    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()

        # Get window dimensions
        window_width = self.winfo_width()
        window_height = self.winfo_height()

        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # Calculate position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def login(self):
        """Handle login attempt"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get()

            # Validate input
            if not username:
                self.show_status("⚠️ Please enter a username", "error")
                self.username_entry.focus()
                return

            if not password:
                self.show_status("⚠️ Please enter a password", "error")
                self.password_entry.focus()
                return

            # Disable login button during authentication
            self.login_button.configure(state="disabled", text="🔄 Logging in...")
            self.show_status("🔍 Authenticating...", "info")
            self.update_idletasks()

            # Authenticate user
            user = User.get_by_username(username)

            if user and user.verify_password(password):
                self.show_status("✅ Login successful!", "success")
                self.after(500, lambda: self.app.login_success(user))
            else:
                self.show_status("❌ Invalid username or password", "error")
                self.password_entry.delete(0, 'end')
                self.password_entry.focus()
                self.login_button.configure(state="normal", text="🚀 Login")

        except Exception as e:
            self.show_status(f"❌ Login error: {str(e)}", "error")
            print(f"Login error: {e}")
            self.login_button.configure(state="normal", text="🚀 Login")

    def show_status(self, message, status_type="info"):
        """Show status message with appropriate color"""
        try:
            colors = {
                "info": ("gray60", "gray40"),
                "success": ("#059669", "#10b981"),
                "error": ("#dc2626", "#ef4444")
            }

            self.status_label.configure(
                text=message,
                text_color=colors.get(status_type, ("gray60", "gray40"))
            )
        except Exception as e:
            print(f"Error showing status: {e}")

    def on_closing(self):
        """Handle window closing"""
        self.app.exit_application()

class MainWindow(ctk.CTk):
    def __init__(self, app, user):
        super().__init__()

        self.app = app
        self.user = user
        self.current_view = None

        self.setup_window()
        self.create_widgets()
        self.load_dashboard()

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window(self):
        """Setup the main window"""
        self.title(f"{app_settings.get_academy_name()} - Advanced Management System")
        self.geometry("1500x900")
        self.minsize(1300, 700)

        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Enhanced sidebar
        self.create_enhanced_sidebar()

        # Main content area with better styling
        self.main_frame = ctk.CTkFrame(self, corner_radius=15)
        self.main_frame.grid(row=0, column=1, padx=(0, 15), pady=15, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

    def create_enhanced_sidebar(self):
        """Create enhanced sidebar with better styling"""
        self.sidebar = ctk.CTkFrame(self, width=320, corner_radius=15)
        self.sidebar.grid(row=0, column=0, padx=(15, 8), pady=15, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)

        # Header section
        header_frame = ctk.CTkFrame(self.sidebar, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=20, pady=(20, 15), sticky="ew")

        # Academy name with icon
        academy_label = ctk.CTkLabel(
            header_frame,
            text=f"🎓 {app_settings.get_academy_name()}",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#1f538d", "#4a9eff")
        )
        academy_label.grid(row=0, column=0, padx=20, pady=(15, 10))

        # User info section
        user_frame = ctk.CTkFrame(header_frame, corner_radius=8)
        user_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"👋 Welcome, {self.user.full_name}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        user_label.grid(row=0, column=0, padx=15, pady=(12, 5))

        # Role badge with color coding
        role_colors = {
            "admin": ("#dc2626", "#ef4444"),
            "staff": ("#059669", "#10b981"),
            "accountant": ("#7c3aed", "#8b5cf6")
        }
        role_color = role_colors.get(self.user.role, ("#6b7280", "#9ca3af"))

        role_label = ctk.CTkLabel(
            user_frame,
            text=f"🏷️ {self.user.role.title()}",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=role_color
        )
        role_label.grid(row=1, column=0, padx=15, pady=(0, 12))

        # Navigation section
        nav_frame = ctk.CTkFrame(self.sidebar, corner_radius=10)
        nav_frame.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="ew")

        nav_title = ctk.CTkLabel(
            nav_frame,
            text="📋 Navigation",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        nav_title.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        # Navigation buttons with enhanced styling
        nav_row = 1

        # Dashboard
        self.create_nav_button(
            nav_frame, "📊 Dashboard", self.load_dashboard, nav_row,
            ("#1f538d", "#4a9eff")
        )
        nav_row += 1

        # Students (available to all roles) - Enhanced
        self.create_nav_button(
            nav_frame, "👥 Students (Advanced)", self.load_students, nav_row,
            ("#059669", "#10b981")
        )
        nav_row += 1

        # Teachers (admin and staff only)
        if self.user.has_permission('teachers'):
            self.create_nav_button(
                nav_frame, "👨‍🏫 Teachers", self.load_teachers, nav_row,
                ("#ea580c", "#f97316")
            )
            nav_row += 1

        # Courses (admin and staff only)
        if self.user.has_permission('courses'):
            self.create_nav_button(
                nav_frame, "📚 Courses", self.load_courses, nav_row,
                ("#7c3aed", "#8b5cf6")
            )
            nav_row += 1

        # Attendance (admin and staff only)
        if self.user.has_permission('attendance'):
            self.create_nav_button(
                nav_frame, "✅ Attendance", self.load_attendance, nav_row,
                ("#0891b2", "#06b6d4")
            )
            nav_row += 1

        # Financial (admin and accountant only)
        if self.user.has_permission('financial'):
            self.create_nav_button(
                nav_frame, "💰 Financial", self.load_financial, nav_row,
                ("#dc2626", "#ef4444")
            )
            nav_row += 1

        # Settings (admin only)
        if self.user.has_permission('all'):
            self.create_nav_button(
                nav_frame, "⚙️ Settings", self.load_settings, nav_row,
                ("#6b7280", "#9ca3af")
            )
            nav_row += 1

        # Add some spacing
        spacer = ctk.CTkLabel(nav_frame, text="", height=10)
        spacer.grid(row=nav_row, column=0, pady=5)

        # Logout button
        logout_btn = ctk.CTkButton(
            self.sidebar,
            text="🚪 Logout",
            command=self.logout,
            width=280,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40"),
            text_color=("gray10", "gray90"),
            corner_radius=10
        )
        logout_btn.grid(row=21, column=0, padx=20, pady=(0, 20))

    def create_nav_button(self, parent, text, command, row, color):
        """Create a navigation button with consistent styling"""
        button = ctk.CTkButton(
            parent,
            text=text,
            command=command,
            width=260,
            height=40,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=color,
            hover_color=(color[0] if isinstance(color, tuple) else color,
                        color[1] if isinstance(color, tuple) else color),
            corner_radius=8,
            anchor="w"
        )
        button.grid(row=row, column=0, padx=20, pady=5, sticky="ew")
        return button

    def clear_main_frame(self):
        """Clear the main content area"""
        try:
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            self.current_view = None
        except Exception as e:
            print(f"Error clearing main frame: {e}")

    def load_dashboard(self):
        """Load enhanced dashboard view"""
        self.clear_main_frame()

        # Header section
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="📊 Advanced Dashboard",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#1f538d", "#4a9eff")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # New features announcement
        announcement_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        announcement_frame.grid(row=1, column=0, padx=25, pady=(0, 15), sticky="ew")

        announcement_text = """🎉 NEW FEATURES AVAILABLE!

✅ Complete Student Management: Add, Edit, Delete students
📸 Photo Upload: Upload and manage student photos
🆔 ID Card Printing: Generate professional student ID cards
🔍 Advanced Search: Enhanced search and filtering
📊 Better Interface: Improved user experience"""

        announcement_label = ctk.CTkLabel(
            announcement_frame,
            text=announcement_text,
            font=ctk.CTkFont(size=14),
            justify="left",
            text_color=("#059669", "#10b981")
        )
        announcement_label.grid(row=0, column=0, padx=25, pady=25, sticky="w")

        # Stats section with enhanced cards
        stats_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        stats_frame.grid(row=2, column=0, padx=25, pady=(0, 15), sticky="ew")
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        try:
            total_students = len(Student.get_all())
            total_teachers = len(Teacher.get_all())
            total_courses = len(Course.get_all())

            # Financial summary
            profit_loss = FinancialTransaction.get_profit_loss()

            # Create enhanced stat cards
            self.create_enhanced_stat_card(stats_frame, "Total Students", str(total_students), "👥", 0, ("#059669", "#10b981"))
            self.create_enhanced_stat_card(stats_frame, "Total Teachers", str(total_teachers), "👨‍🏫", 1, ("#ea580c", "#f97316"))
            self.create_enhanced_stat_card(stats_frame, "Total Courses", str(total_courses), "📚", 2, ("#7c3aed", "#8b5cf6"))
            self.create_enhanced_stat_card(stats_frame, "Monthly Profit", f"${profit_loss['profit_loss']:.2f}", "💰", 3, ("#dc2626", "#ef4444"))

        except Exception as e:
            error_label = ctk.CTkLabel(
                stats_frame,
                text=f"❌ Error loading statistics: {e}",
                text_color=("#dc2626", "#ef4444"),
                font=ctk.CTkFont(size=14)
            )
            error_label.grid(row=0, column=0, columnspan=4, padx=25, pady=25)

        # Quick actions section
        actions_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        actions_frame.grid(row=3, column=0, padx=25, pady=(0, 25), sticky="ew")
        actions_frame.grid_columnconfigure(0, weight=1)

        actions_title = ctk.CTkLabel(
            actions_frame,
            text="⚡ Quick Actions",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        actions_title.grid(row=0, column=0, padx=25, pady=(20, 15), sticky="w")

        # Enhanced quick action buttons
        buttons_frame = ctk.CTkFrame(actions_frame, corner_radius=8)
        buttons_frame.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

        if self.user.has_permission('students'):
            add_student_btn = ctk.CTkButton(
                buttons_frame,
                text="👥 Manage Students (NEW!)",
                command=self.load_students,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=("#059669", "#10b981"),
                hover_color=("#047857", "#059669"),
                corner_radius=8
            )
            add_student_btn.grid(row=0, column=0, padx=15, pady=15, sticky="ew")

        if self.user.has_permission('attendance'):
            record_attendance_btn = ctk.CTkButton(
                buttons_frame,
                text="✅ Record Attendance",
                command=self.load_attendance,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=("#0891b2", "#06b6d4"),
                hover_color=("#0e7490", "#0891b2"),
                corner_radius=8
            )
            record_attendance_btn.grid(row=0, column=1, padx=15, pady=15, sticky="ew")

        if self.user.has_permission('financial'):
            financial_btn = ctk.CTkButton(
                buttons_frame,
                text="💰 Financial Reports",
                command=self.load_financial,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=("#dc2626", "#ef4444"),
                hover_color=("#b91c1c", "#dc2626"),
                corner_radius=8
            )
            financial_btn.grid(row=0, column=2, padx=15, pady=15, sticky="ew")

    def create_enhanced_stat_card(self, parent, title, value, icon, column, color):
        """Create an enhanced statistics card"""
        card_frame = ctk.CTkFrame(parent, corner_radius=12)
        card_frame.grid(row=0, column=column, padx=15, pady=25, sticky="ew")

        # Icon with colored background
        icon_frame = ctk.CTkFrame(card_frame, corner_radius=8, fg_color=color)
        icon_frame.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")

        icon_label = ctk.CTkLabel(
            icon_frame,
            text=icon,
            font=ctk.CTkFont(size=28),
            text_color="white"
        )
        icon_label.grid(row=0, column=0, padx=15, pady=10)

        # Value
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=color
        )
        value_label.grid(row=1, column=0, padx=20, pady=5)

        # Title
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=("gray60", "gray40")
        )
        title_label.grid(row=2, column=0, padx=20, pady=(5, 20))

    def load_students(self):
        """Load advanced students management view with all CRUD operations"""
        self.clear_main_frame()

        # Header section
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="👥 Advanced Student Management",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#059669", "#10b981")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Controls section with all CRUD operations
        controls_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        controls_frame.grid(row=1, column=0, padx=25, pady=(0, 15), sticky="ew")
        controls_frame.grid_columnconfigure(1, weight=1)

        # Search section
        search_label = ctk.CTkLabel(
            controls_frame,
            text="🔍 Search:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        search_label.grid(row=0, column=0, padx=(25, 15), pady=20)

        self.search_entry = ctk.CTkEntry(
            controls_frame,
            placeholder_text="Search by name, ID, phone, or address...",
            height=35,
            font=ctk.CTkFont(size=13),
            corner_radius=8
        )
        self.search_entry.grid(row=0, column=1, padx=(0, 15), pady=20, sticky="ew")
        self.search_entry.bind("<KeyRelease>", self.on_search_change)

        # Action buttons
        search_btn = ctk.CTkButton(
            controls_frame,
            text="🔍 Search",
            command=self.search_students,
            width=100,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#0891b2", "#06b6d4"),
            hover_color=("#0e7490", "#0891b2"),
            corner_radius=8
        )
        search_btn.grid(row=0, column=2, padx=(0, 10), pady=20)

        add_btn = ctk.CTkButton(
            controls_frame,
            text="➕ Add Student",
            command=self.add_student,
            width=130,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#059669", "#10b981"),
            hover_color=("#047857", "#059669"),
            corner_radius=8
        )
        add_btn.grid(row=0, column=3, padx=(0, 10), pady=20)

        edit_btn = ctk.CTkButton(
            controls_frame,
            text="✏️ Edit Selected",
            command=self.edit_selected_student,
            width=130,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#ea580c", "#f97316"),
            hover_color=("#c2410c", "#ea580c"),
            corner_radius=8
        )
        edit_btn.grid(row=0, column=4, padx=(0, 10), pady=20)

        delete_btn = ctk.CTkButton(
            controls_frame,
            text="🗑️ Delete",
            command=self.delete_selected_student,
            width=100,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#dc2626", "#ef4444"),
            hover_color=("#b91c1c", "#dc2626"),
            corner_radius=8
        )
        delete_btn.grid(row=0, column=5, padx=(0, 10), pady=20)

        print_btn = ctk.CTkButton(
            controls_frame,
            text="🆔 Print ID",
            command=self.print_student_id,
            width=100,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#7c3aed", "#8b5cf6"),
            hover_color=("#6d28d9", "#7c3aed"),
            corner_radius=8
        )
        print_btn.grid(row=0, column=6, padx=(0, 25), pady=20)

        # Students table section
        table_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        table_frame.grid(row=2, column=0, padx=25, pady=(0, 25), sticky="nsew")
        table_frame.grid_columnconfigure(0, weight=1)
        table_frame.grid_rowconfigure(1, weight=1)

        # Table header with count
        table_header_frame = ctk.CTkFrame(table_frame, fg_color="transparent")
        table_header_frame.grid(row=0, column=0, padx=25, pady=(20, 10), sticky="ew")
        table_header_frame.grid_columnconfigure(1, weight=1)

        table_header = ctk.CTkLabel(
            table_header_frame,
            text="📋 Students List",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        table_header.grid(row=0, column=0, sticky="w")

        self.student_count_label = ctk.CTkLabel(
            table_header_frame,
            text="",
            font=ctk.CTkFont(size=14),
            text_color=("#059669", "#10b981")
        )
        self.student_count_label.grid(row=0, column=1, sticky="e")

        # Create enhanced treeview with photo support
        self.create_advanced_students_table(table_frame)

        # Load students data
        self.load_students_data()

    def create_advanced_students_table(self, parent):
        """Create advanced students table with photo support"""
        # Create frame for table
        tree_container = ctk.CTkFrame(parent, corner_radius=8)
        tree_container.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="nsew")
        tree_container.grid_columnconfigure(0, weight=1)
        tree_container.grid_rowconfigure(0, weight=1)

        # Create style for treeview
        style = ttk.Style()
        style.theme_use("clam")

        # Configure treeview colors for dark theme
        style.configure("Treeview",
                       background="#2b2b2b",
                       foreground="white",
                       fieldbackground="#2b2b2b",
                       borderwidth=0,
                       font=("Segoe UI", 11))

        style.configure("Treeview.Heading",
                       background="#1f538d",
                       foreground="white",
                       borderwidth=1,
                       font=("Segoe UI", 11, "bold"))

        style.map("Treeview",
                 background=[('selected', '#4a9eff')],
                 foreground=[('selected', 'white')])

        # Create treeview with enhanced columns including photo status
        columns = ("ID", "Name", "Group", "Level", "Phone", "Address", "Photo", "Status", "Joined")
        self.students_tree = ttk.Treeview(
            tree_container,
            columns=columns,
            show="headings",
            height=15,
            style="Treeview"
        )

        # Configure column headings and widths
        column_configs = {
            "ID": ("Student ID", 100),
            "Name": ("Full Name", 180),
            "Group": ("Group", 120),
            "Level": ("Level", 80),
            "Phone": ("Parent Phone", 120),
            "Address": ("Address", 150),
            "Photo": ("Photo", 80),
            "Status": ("Status", 100),
            "Joined": ("Joined Date", 100)
        }

        for col, (heading, width) in column_configs.items():
            self.students_tree.heading(col, text=heading)
            self.students_tree.column(col, width=width, minwidth=width//2)

        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(tree_container, orient="vertical", command=self.students_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_container, orient="horizontal", command=self.students_tree.xview)

        self.students_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid treeview and scrollbars
        self.students_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # Bind events
        self.students_tree.bind("<Double-1>", self.edit_student_double_click)
        self.students_tree.bind("<Button-3>", self.show_context_menu)  # Right-click
        self.students_tree.bind("<<TreeviewSelect>>", self.on_student_select)

    def load_students_data(self):
        """Load students data with enhanced display including photo status"""
        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # Get all students
            students = Student.get_all()

            # Populate treeview with enhanced data
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                # Check photo status
                photo_path = os.path.join("student_photos", f"{student.student_id}.jpg")
                photo_status = "📸 Yes" if os.path.exists(photo_path) else "❌ No"

                # Determine status
                status = "✅ Active" if student.is_active else "⚠️ Inactive"

                # Format join date
                try:
                    if student.enrollment_date:
                        if isinstance(student.enrollment_date, str):
                            join_date = student.enrollment_date
                        else:
                            join_date = student.enrollment_date.strftime("%Y-%m-%d")
                    else:
                        join_date = "N/A"
                except:
                    join_date = "N/A"

                # Insert with enhanced data
                item_id = self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A",
                    student.address or "N/A",
                    photo_status,
                    status,
                    join_date
                ))

                # Store student ID for easy access (can't store objects in treeview)

            # Update count label
            self.student_count_label.configure(text=f"Total: {len(students)} students")
            print(f"✓ Loaded {len(students)} students with enhanced data")

        except Exception as e:
            print(f"Error loading students: {e}")
            messagebox.showerror("Error", f"Failed to load students: {e}")

    def on_search_change(self, event=None):
        """Handle real-time search with debouncing"""
        if hasattr(self, '_search_timer'):
            self.after_cancel(self._search_timer)
        self._search_timer = self.after(300, self.search_students)  # 300ms delay

    def search_students(self):
        """Enhanced search functionality with multiple criteria"""
        search_term = self.search_entry.get().strip()

        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            if search_term:
                students = Student.search(search_term)
                search_status = f"Found {len(students)} students matching '{search_term}'"
            else:
                students = Student.get_all()
                search_status = f"Showing all {len(students)} students"

            # Populate treeview with search results
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                # Check photo status
                photo_path = os.path.join("student_photos", f"{student.student_id}.jpg")
                photo_status = "📸 Yes" if os.path.exists(photo_path) else "❌ No"

                # Determine status
                status = "✅ Active" if student.is_active else "⚠️ Inactive"

                # Format join date
                try:
                    if student.enrollment_date:
                        if isinstance(student.enrollment_date, str):
                            join_date = student.enrollment_date
                        else:
                            join_date = student.enrollment_date.strftime("%Y-%m-%d")
                    else:
                        join_date = "N/A"
                except:
                    join_date = "N/A"

                self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A",
                    student.address or "N/A",
                    photo_status,
                    status,
                    join_date
                ))

            # Update count label
            self.student_count_label.configure(text=f"Found: {len(students)} students")
            print(search_status)

        except Exception as e:
            print(f"Search error: {e}")
            messagebox.showerror("Search Error", f"Search failed: {e}")

    def on_student_select(self, event=None):
        """Handle student selection"""
        selection = self.students_tree.selection()
        if selection:
            # Enable/disable buttons based on selection
            pass

    def get_selected_student(self):
        """Get the currently selected student"""
        selection = self.students_tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a student first.")
            return None

        item = self.students_tree.item(selection[0])
        student_id = item['values'][0]

        # Get student object from database
        student = Student.get_by_student_id(student_id)
        return student

    def add_student(self):
        """Open add student dialog"""
        try:
            dialog = AddStudentDialog(self)
            if dialog.result:
                # Refresh the students list
                self.load_students_data()
                messagebox.showinfo("Success", f"Student {dialog.result['full_name']} added successfully!")
        except Exception as e:
            print(f"Error adding student: {e}")
            messagebox.showerror("Error", f"Failed to add student: {e}")

    def edit_selected_student(self):
        """Edit the selected student"""
        student = self.get_selected_student()
        if student:
            self.edit_student(student)

    def edit_student_double_click(self, event=None):
        """Handle double-click to edit student"""
        student = self.get_selected_student()
        if student:
            self.edit_student(student)

    def edit_student(self, student):
        """Open edit student dialog"""
        try:
            dialog = EditStudentDialog(self, student)
            if dialog.result:
                # Refresh the students list
                self.load_students_data()
                messagebox.showinfo("Success", f"Student {student.full_name} updated successfully!")
        except Exception as e:
            print(f"Error editing student: {e}")
            messagebox.showerror("Error", f"Failed to edit student: {e}")

    def delete_selected_student(self):
        """Delete the selected student with confirmation"""
        student = self.get_selected_student()
        if not student:
            return

        # Confirmation dialog
        result = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete student:\n\n"
            f"Name: {student.full_name}\n"
            f"ID: {student.student_id}\n\n"
            f"⚠️ This action cannot be undone!",
            icon="warning"
        )

        if result:
            try:
                # Delete student photo if exists
                photo_path = os.path.join("student_photos", f"{student.student_id}.jpg")
                if os.path.exists(photo_path):
                    os.remove(photo_path)

                # Delete student from database
                student.delete()

                # Refresh the students list
                self.load_students_data()

                messagebox.showinfo("Success", f"Student {student.full_name} deleted successfully!")

            except Exception as e:
                print(f"Error deleting student: {e}")
                messagebox.showerror("Error", f"Failed to delete student: {e}")

    def print_student_id(self):
        """Print ID card for selected student"""
        student = self.get_selected_student()
        if not student:
            return

        try:
            # Generate ID card
            card_generator = StudentIDCardGenerator()
            card_path = card_generator.generate_id_card(student)

            if card_path:
                messagebox.showinfo(
                    "ID Card Generated",
                    f"ID card generated successfully!\n\nSaved to: {card_path}\n\nYou can now print the PDF file."
                )

                # Ask if user wants to open the file
                if messagebox.askyesno("Open File", "Would you like to open the ID card file?"):
                    import subprocess
                    subprocess.run(['start', card_path], shell=True)

        except Exception as e:
            print(f"Error generating ID card: {e}")
            messagebox.showerror("Error", f"Failed to generate ID card: {e}")

    def show_context_menu(self, event):
        """Show context menu for student operations"""
        # Create context menu
        context_menu = tk.Menu(self, tearoff=0)
        context_menu.add_command(label="✏️ Edit Student", command=self.edit_selected_student)
        context_menu.add_command(label="📸 Upload Photo", command=self.upload_student_photo)
        context_menu.add_command(label="🆔 Print ID Card", command=self.print_student_id)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ Delete Student", command=self.delete_selected_student)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def upload_student_photo(self):
        """Upload photo for selected student"""
        student = self.get_selected_student()
        if not student:
            return

        try:
            # Open file dialog
            file_path = filedialog.askopenfilename(
                title="Select Student Photo",
                filetypes=[
                    ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif"),
                    ("JPEG files", "*.jpg *.jpeg"),
                    ("PNG files", "*.png"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                # Process and save the photo
                photo_processor = StudentPhotoProcessor()
                success = photo_processor.process_and_save_photo(file_path, student.student_id)

                if success:
                    # Refresh the students list to update photo status
                    self.load_students_data()
                    messagebox.showinfo("Success", f"Photo uploaded successfully for {student.full_name}!")
                else:
                    messagebox.showerror("Error", "Failed to process and save the photo.")

        except Exception as e:
            print(f"Error uploading photo: {e}")
            messagebox.showerror("Error", f"Failed to upload photo: {e}")

    # Placeholder methods for other sections (will be implemented later)
    def load_teachers(self):
        """Load teachers view - placeholder"""
        self.clear_main_frame()
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="👨‍🏫 Teachers Management\n\nComing in next update...",
            font=ctk.CTkFont(size=20)
        )
        placeholder_label.grid(row=0, column=0, padx=50, pady=50)

    def load_courses(self):
        """Load courses view - placeholder"""
        self.clear_main_frame()
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="📚 Courses Management\n\nComing in next update...",
            font=ctk.CTkFont(size=20)
        )
        placeholder_label.grid(row=0, column=0, padx=50, pady=50)

    def load_attendance(self):
        """Load attendance view - placeholder"""
        self.clear_main_frame()
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="✅ Attendance Management\n\nComing in next update...",
            font=ctk.CTkFont(size=20)
        )
        placeholder_label.grid(row=0, column=0, padx=50, pady=50)

    def load_financial(self):
        """Load financial view - placeholder"""
        self.clear_main_frame()
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="💰 Financial Management\n\nComing in next update...",
            font=ctk.CTkFont(size=20)
        )
        placeholder_label.grid(row=0, column=0, padx=50, pady=50)

    def load_settings(self):
        """Load settings view - placeholder"""
        self.clear_main_frame()
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="⚙️ Settings\n\nComing in next update...",
            font=ctk.CTkFont(size=20)
        )
        placeholder_label.grid(row=0, column=0, padx=50, pady=50)

    def logout(self):
        """Handle logout with confirmation"""
        if messagebox.askyesno("Logout Confirmation", "🚪 Are you sure you want to logout?"):
            self.app.logout()

    def on_closing(self):
        """Handle window closing with confirmation"""
        if messagebox.askyesno("Exit Confirmation", "❌ Are you sure you want to exit the application?"):
            self.app.exit_application()

class AddStudentDialog(ctk.CTkToplevel):
    def __init__(self, parent):
        super().__init__(parent)

        self.parent = parent
        self.result = None

        self.setup_window()
        self.create_widgets()
        self.center_window()

        # Make dialog modal
        self.transient(parent)
        self.grab_set()

        # Focus on first entry
        self.after(100, lambda: self.full_name_entry.focus())

    def setup_window(self):
        """Setup the dialog window"""
        self.title("➕ Add New Student")
        self.geometry("600x700")
        self.resizable(False, False)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self, corner_radius=15)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Header
        header_label = ctk.CTkLabel(
            main_frame,
            text="👥 Add New Student",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=("#059669", "#10b981")
        )
        header_label.grid(row=0, column=0, padx=20, pady=(20, 30))

        # Form frame
        form_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        form_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        form_frame.grid_columnconfigure(1, weight=1)

        row = 0

        # Full Name
        ctk.CTkLabel(form_frame, text="👤 Full Name:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=(20, 10), sticky="w"
        )
        self.full_name_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter student's full name", width=300)
        self.full_name_entry.grid(row=row, column=1, padx=(0, 20), pady=(20, 10), sticky="ew")
        row += 1

        # Date of Birth
        ctk.CTkLabel(form_frame, text="📅 Date of Birth:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.dob_entry = ctk.CTkEntry(form_frame, placeholder_text="YYYY-MM-DD", width=300)
        self.dob_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Gender
        ctk.CTkLabel(form_frame, text="⚧ Gender:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.gender_var = ctk.StringVar(value="Male")
        gender_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        gender_frame.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")

        ctk.CTkRadioButton(gender_frame, text="Male", variable=self.gender_var, value="Male").grid(row=0, column=0, padx=(0, 20))
        ctk.CTkRadioButton(gender_frame, text="Female", variable=self.gender_var, value="Female").grid(row=0, column=1)
        row += 1

        # Parent Name
        ctk.CTkLabel(form_frame, text="👨‍👩‍👧‍👦 Parent Name:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.parent_name_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter parent's name", width=300)
        self.parent_name_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Parent Phone
        ctk.CTkLabel(form_frame, text="📞 Parent Phone:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.parent_phone_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter phone number", width=300)
        self.parent_phone_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Address
        ctk.CTkLabel(form_frame, text="🏠 Address:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.address_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter address", width=300)
        self.address_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Course
        ctk.CTkLabel(form_frame, text="📚 Course:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.course_var = ctk.StringVar(value="Mathematics")
        course_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.course_var,
            values=["Mathematics", "English", "Science", "Computer Science"]
        )
        course_menu.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Level
        ctk.CTkLabel(form_frame, text="📊 Level:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.level_var = ctk.StringVar(value="1")
        level_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.level_var,
            values=["1", "2", "3", "4", "5"]
        )
        level_menu.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Emergency Contact
        ctk.CTkLabel(form_frame, text="🚨 Emergency Contact:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.emergency_contact_entry = ctk.CTkEntry(form_frame, placeholder_text="Emergency contact number", width=300)
        self.emergency_contact_entry.grid(row=row, column=1, padx=(0, 20), pady=(10, 20), sticky="ew")

        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.grid(row=2, column=0, padx=20, pady=(0, 20), sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1), weight=1)

        # Cancel button
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ Cancel",
            command=self.cancel,
            width=150,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        cancel_btn.grid(row=0, column=0, padx=(0, 10), pady=10)

        # Save button
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 Save Student",
            command=self.save_student,
            width=150,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#059669", "#10b981"),
            hover_color=("#047857", "#059669")
        )
        save_btn.grid(row=0, column=1, padx=(10, 0), pady=10)

    def center_window(self):
        """Center the dialog on parent window"""
        self.update_idletasks()

        # Get dialog dimensions
        dialog_width = self.winfo_width()
        dialog_height = self.winfo_height()

        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def validate_input(self):
        """Validate form input"""
        if not self.full_name_entry.get().strip():
            messagebox.showerror("Validation Error", "Please enter the student's full name.")
            self.full_name_entry.focus()
            return False

        if not self.parent_phone_entry.get().strip():
            messagebox.showerror("Validation Error", "Please enter the parent's phone number.")
            self.parent_phone_entry.focus()
            return False

        # Validate date format
        dob = self.dob_entry.get().strip()
        if dob:
            try:
                datetime.strptime(dob, "%Y-%m-%d")
            except ValueError:
                messagebox.showerror("Validation Error", "Please enter date in YYYY-MM-DD format.")
                self.dob_entry.focus()
                return False

        return True

    def save_student(self):
        """Save the new student"""
        if not self.validate_input():
            return

        try:
            # Generate unique student ID
            student_id = f"STU{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Create student data
            student_data = {
                'student_id': student_id,
                'full_name': self.full_name_entry.get().strip(),
                'date_of_birth': self.dob_entry.get().strip() or None,
                'gender': self.gender_var.get(),
                'parent_name': self.parent_name_entry.get().strip(),
                'parent_phone': self.parent_phone_entry.get().strip(),
                'address': self.address_entry.get().strip(),
                'current_level': int(self.level_var.get()),
                'emergency_contact': self.emergency_contact_entry.get().strip(),
                'enrollment_date': datetime.now().date(),
                'is_active': True
            }

            # Create student in database
            student = Student.create(**student_data)

            if student:
                self.result = student_data
                self.destroy()
            else:
                messagebox.showerror("Error", "Failed to create student in database.")

        except Exception as e:
            print(f"Error saving student: {e}")
            messagebox.showerror("Error", f"Failed to save student: {e}")

    def cancel(self):
        """Cancel the dialog"""
        self.result = None
        self.destroy()

class EditStudentDialog(ctk.CTkToplevel):
    def __init__(self, parent, student):
        super().__init__(parent)

        self.parent = parent
        self.student = student
        self.result = None

        self.setup_window()
        self.create_widgets()
        self.populate_fields()
        self.center_window()

        # Make dialog modal
        self.transient(parent)
        self.grab_set()

        # Focus on first entry
        self.after(100, lambda: self.full_name_entry.focus())

    def setup_window(self):
        """Setup the dialog window"""
        self.title(f"✏️ Edit Student - {self.student.full_name}")
        self.geometry("600x750")
        self.resizable(False, False)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self, corner_radius=15)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Header
        header_label = ctk.CTkLabel(
            main_frame,
            text=f"✏️ Edit Student: {self.student.full_name}",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=("#ea580c", "#f97316")
        )
        header_label.grid(row=0, column=0, padx=20, pady=(20, 30))

        # Form frame
        form_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        form_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        form_frame.grid_columnconfigure(1, weight=1)

        row = 0

        # Student ID (read-only)
        ctk.CTkLabel(form_frame, text="🆔 Student ID:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=(20, 10), sticky="w"
        )
        self.student_id_label = ctk.CTkLabel(
            form_frame,
            text=self.student.student_id,
            font=ctk.CTkFont(size=14),
            text_color=("#059669", "#10b981")
        )
        self.student_id_label.grid(row=row, column=1, padx=(0, 20), pady=(20, 10), sticky="w")
        row += 1

        # Full Name
        ctk.CTkLabel(form_frame, text="👤 Full Name:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.full_name_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter student's full name", width=300)
        self.full_name_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Date of Birth
        ctk.CTkLabel(form_frame, text="📅 Date of Birth:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.dob_entry = ctk.CTkEntry(form_frame, placeholder_text="YYYY-MM-DD", width=300)
        self.dob_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Gender
        ctk.CTkLabel(form_frame, text="⚧ Gender:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.gender_var = ctk.StringVar(value="Male")
        gender_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        gender_frame.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")

        ctk.CTkRadioButton(gender_frame, text="Male", variable=self.gender_var, value="Male").grid(row=0, column=0, padx=(0, 20))
        ctk.CTkRadioButton(gender_frame, text="Female", variable=self.gender_var, value="Female").grid(row=0, column=1)
        row += 1

        # Parent Name
        ctk.CTkLabel(form_frame, text="👨‍👩‍👧‍👦 Parent Name:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.parent_name_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter parent's name", width=300)
        self.parent_name_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Parent Phone
        ctk.CTkLabel(form_frame, text="📞 Parent Phone:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.parent_phone_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter phone number", width=300)
        self.parent_phone_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Address
        ctk.CTkLabel(form_frame, text="🏠 Address:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.address_entry = ctk.CTkEntry(form_frame, placeholder_text="Enter address", width=300)
        self.address_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Level
        ctk.CTkLabel(form_frame, text="📊 Level:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.level_var = ctk.StringVar(value="1")
        level_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.level_var,
            values=["1", "2", "3", "4", "5"]
        )
        level_menu.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Emergency Contact
        ctk.CTkLabel(form_frame, text="🚨 Emergency Contact:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.emergency_contact_entry = ctk.CTkEntry(form_frame, placeholder_text="Emergency contact number", width=300)
        self.emergency_contact_entry.grid(row=row, column=1, padx=(0, 20), pady=10, sticky="ew")
        row += 1

        # Status
        ctk.CTkLabel(form_frame, text="📊 Status:", font=ctk.CTkFont(size=14, weight="bold")).grid(
            row=row, column=0, padx=(20, 10), pady=10, sticky="w"
        )
        self.status_var = ctk.StringVar(value="Active")
        status_menu = ctk.CTkOptionMenu(
            form_frame,
            variable=self.status_var,
            values=["Active", "Inactive"]
        )
        status_menu.grid(row=row, column=1, padx=(0, 20), pady=(10, 20), sticky="ew")

        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.grid(row=2, column=0, padx=20, pady=(0, 20), sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

        # Cancel button
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ Cancel",
            command=self.cancel,
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        cancel_btn.grid(row=0, column=0, padx=5, pady=10)

        # Upload Photo button
        photo_btn = ctk.CTkButton(
            buttons_frame,
            text="📸 Upload Photo",
            command=self.upload_photo,
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#7c3aed", "#8b5cf6"),
            hover_color=("#6d28d9", "#7c3aed")
        )
        photo_btn.grid(row=0, column=1, padx=5, pady=10)

        # Save button
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 Save Changes",
            command=self.save_changes,
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("#ea580c", "#f97316"),
            hover_color=("#c2410c", "#ea580c")
        )
        save_btn.grid(row=0, column=2, padx=5, pady=10)

    def populate_fields(self):
        """Populate form fields with student data"""
        self.full_name_entry.insert(0, self.student.full_name or "")

        if self.student.date_of_birth:
            self.dob_entry.insert(0, str(self.student.date_of_birth))

        if self.student.gender:
            self.gender_var.set(self.student.gender)

        self.parent_name_entry.insert(0, self.student.parent_name or "")
        self.parent_phone_entry.insert(0, self.student.parent_phone or "")
        self.address_entry.insert(0, self.student.address or "")
        self.level_var.set(str(self.student.current_level))
        self.emergency_contact_entry.insert(0, self.student.emergency_contact or "")
        self.status_var.set("Active" if self.student.is_active else "Inactive")

    def center_window(self):
        """Center the dialog on parent window"""
        self.update_idletasks()

        # Get dialog dimensions
        dialog_width = self.winfo_width()
        dialog_height = self.winfo_height()

        # Get parent window position and size
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # Calculate center position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def validate_input(self):
        """Validate form input"""
        if not self.full_name_entry.get().strip():
            messagebox.showerror("Validation Error", "Please enter the student's full name.")
            self.full_name_entry.focus()
            return False

        if not self.parent_phone_entry.get().strip():
            messagebox.showerror("Validation Error", "Please enter the parent's phone number.")
            self.parent_phone_entry.focus()
            return False

        # Validate date format
        dob = self.dob_entry.get().strip()
        if dob:
            try:
                datetime.strptime(dob, "%Y-%m-%d")
            except ValueError:
                messagebox.showerror("Validation Error", "Please enter date in YYYY-MM-DD format.")
                self.dob_entry.focus()
                return False

        return True

    def upload_photo(self):
        """Upload photo for this student"""
        try:
            # Open file dialog
            file_path = filedialog.askopenfilename(
                title="Select Student Photo",
                filetypes=[
                    ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif"),
                    ("JPEG files", "*.jpg *.jpeg"),
                    ("PNG files", "*.png"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                # Process and save the photo
                photo_processor = StudentPhotoProcessor()
                success = photo_processor.process_and_save_photo(file_path, self.student.student_id)

                if success:
                    messagebox.showinfo("Success", f"Photo uploaded successfully for {self.student.full_name}!")
                else:
                    messagebox.showerror("Error", "Failed to process and save the photo.")

        except Exception as e:
            print(f"Error uploading photo: {e}")
            messagebox.showerror("Error", f"Failed to upload photo: {e}")

    def save_changes(self):
        """Save the changes to the student"""
        if not self.validate_input():
            return

        try:
            # Update student data
            self.student.full_name = self.full_name_entry.get().strip()

            dob = self.dob_entry.get().strip()
            if dob:
                self.student.date_of_birth = datetime.strptime(dob, "%Y-%m-%d").date()

            self.student.gender = self.gender_var.get()
            self.student.parent_name = self.parent_name_entry.get().strip()
            self.student.parent_phone = self.parent_phone_entry.get().strip()
            self.student.address = self.address_entry.get().strip()
            self.student.current_level = int(self.level_var.get())
            self.student.emergency_contact = self.emergency_contact_entry.get().strip()
            self.student.is_active = (self.status_var.get() == "Active")

            # Save to database
            success = self.student.save()

            if success:
                self.result = True
                self.destroy()
            else:
                messagebox.showerror("Error", "Failed to save changes to database.")

        except Exception as e:
            print(f"Error saving changes: {e}")
            messagebox.showerror("Error", f"Failed to save changes: {e}")

    def cancel(self):
        """Cancel the dialog"""
        self.result = None
        self.destroy()

class StudentPhotoProcessor:
    """Handle student photo processing and storage"""

    def __init__(self):
        self.photo_dir = "student_photos"
        self.max_size = (300, 400)  # Max photo size
        self.quality = 85  # JPEG quality

    def process_and_save_photo(self, source_path, student_id):
        """Process and save student photo"""
        try:
            # Open and process the image
            with Image.open(source_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Resize image while maintaining aspect ratio
                img.thumbnail(self.max_size, Image.Resampling.LANCZOS)

                # Create a new image with white background
                new_img = Image.new('RGB', self.max_size, 'white')

                # Calculate position to center the image
                x = (self.max_size[0] - img.width) // 2
                y = (self.max_size[1] - img.height) // 2

                # Paste the resized image onto the white background
                new_img.paste(img, (x, y))

                # Save the processed image
                output_path = os.path.join(self.photo_dir, f"{student_id}.jpg")
                new_img.save(output_path, 'JPEG', quality=self.quality, optimize=True)

                print(f"✓ Photo saved: {output_path}")
                return True

        except Exception as e:
            print(f"Error processing photo: {e}")
            return False

    def get_photo_path(self, student_id):
        """Get the path to a student's photo"""
        photo_path = os.path.join(self.photo_dir, f"{student_id}.jpg")
        return photo_path if os.path.exists(photo_path) else None

    def delete_photo(self, student_id):
        """Delete a student's photo"""
        try:
            photo_path = os.path.join(self.photo_dir, f"{student_id}.jpg")
            if os.path.exists(photo_path):
                os.remove(photo_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting photo: {e}")
            return False

class StudentIDCardGenerator:
    """Generate student ID cards in PDF format"""

    def __init__(self):
        self.cards_dir = "student_cards"
        self.card_width = 300  # pixels
        self.card_height = 200  # pixels

    def generate_id_card(self, student):
        """Generate ID card for a student"""
        try:
            # For now, create a simple text-based ID card
            # In a full implementation, you would use reportlab for PDF generation

            card_content = f"""
🎓 {app_settings.get_academy_name()}
═══════════════════════════════════════

📋 STUDENT ID CARD

🆔 Student ID: {student.student_id}
👤 Name: {student.full_name}
📊 Level: {student.current_level}
📞 Parent Phone: {student.parent_phone or 'N/A'}
📅 Enrolled: {student.enrollment_date or 'N/A'}

═══════════════════════════════════════
This card is valid for the current academic year.
Please contact the academy for any inquiries.
"""

            # Save as text file (in a real implementation, this would be PDF)
            card_filename = f"{student.student_id}_id_card.txt"
            card_path = os.path.join(self.cards_dir, card_filename)

            with open(card_path, 'w', encoding='utf-8') as f:
                f.write(card_content)

            print(f"✓ ID card generated: {card_path}")
            return card_path

        except Exception as e:
            print(f"Error generating ID card: {e}")
            return None

    def generate_pdf_card(self, student):
        """Generate PDF ID card (placeholder for future implementation)"""
        # This would use reportlab to create a professional PDF ID card
        # with student photo, barcode, academy logo, etc.
        pass

def main():
    """Main entry point"""
    print("=" * 70)
    print("🎓 Educational Academy Management System - Advanced Version")
    print("=" * 70)
    print("🆕 NEW FEATURES:")
    print("✅ Complete Student Management (Add/Edit/Delete)")
    print("📸 Photo Upload and Management")
    print("🆔 Student ID Card Generation")
    print("🔍 Advanced Search and Filtering")
    print("📊 Enhanced User Interface")
    print("=" * 70)
    print("Starting application...")

    try:
        app = AcademyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        messagebox.showerror("Fatal Error", f"Application failed to start: {e}")

if __name__ == "__main__":
    main()