from datetime import datetime
from config.database import db_manager

class Group:
    def __init__(self, id=None, name=None, course_id=None, level=1, teacher_id=None,
                 max_students=20, created_at=None, is_active=True):
        self.id = id
        self.name = name
        self.course_id = course_id
        self.level = level
        self.teacher_id = teacher_id
        self.max_students = max_students
        self.created_at = created_at
        self.is_active = is_active
    
    @classmethod
    def create(cls, name, course_id, level=1, teacher_id=None, max_students=20):
        """Create a new group"""
        new_id = db_manager.execute_update(
            '''INSERT INTO groups (name, course_id, level, teacher_id, max_students)
               VALUES (?, ?, ?, ?, ?)''',
            (name, course_id, level, teacher_id, max_students)
        )
        return cls.get_by_id(new_id)
    
    @classmethod
    def get_by_id(cls, group_id):
        """Get group by ID"""
        result = db_manager.execute_query(
            "SELECT * FROM groups WHERE id = ? AND is_active = 1", (group_id,)
        )
        if result:
            return cls._from_row(result[0])
        return None
    
    @classmethod
    def get_all(cls):
        """Get all active groups"""
        results = db_manager.execute_query(
            '''SELECT g.*, c.name as course_name, t.full_name as teacher_name
               FROM groups g
               JOIN courses c ON g.course_id = c.id
               LEFT JOIN teachers t ON g.teacher_id = t.id
               WHERE g.is_active = 1
               ORDER BY c.name, g.level, g.name'''
        )
        return results
    
    @classmethod
    def get_by_course(cls, course_id):
        """Get groups by course"""
        results = db_manager.execute_query(
            '''SELECT g.*, t.full_name as teacher_name
               FROM groups g
               LEFT JOIN teachers t ON g.teacher_id = t.id
               WHERE g.course_id = ? AND g.is_active = 1
               ORDER BY g.level, g.name''',
            (course_id,)
        )
        return results
    
    @classmethod
    def get_by_teacher(cls, teacher_id):
        """Get groups assigned to a teacher"""
        results = db_manager.execute_query(
            '''SELECT g.*, c.name as course_name
               FROM groups g
               JOIN courses c ON g.course_id = c.id
               WHERE g.teacher_id = ? AND g.is_active = 1
               ORDER BY c.name, g.level''',
            (teacher_id,)
        )
        return results
    
    @classmethod
    def _from_row(cls, row):
        """Create Group instance from database row"""
        return cls(
            id=row[0], name=row[1], course_id=row[2], level=row[3],
            teacher_id=row[4], max_students=row[5], created_at=row[6], is_active=row[7]
        )
    
    def update(self, **kwargs):
        """Update group information"""
        valid_fields = ['name', 'level', 'teacher_id', 'max_students']
        
        update_fields = []
        update_values = []
        
        for field, value in kwargs.items():
            if field in valid_fields:
                update_fields.append(f"{field} = ?")
                update_values.append(value)
                setattr(self, field, value)
        
        if update_fields:
            update_values.append(self.id)
            query = f"UPDATE groups SET {', '.join(update_fields)} WHERE id = ?"
            db_manager.execute_update(query, update_values)
    
    def get_students(self):
        """Get students in this group"""
        results = db_manager.execute_query(
            '''SELECT * FROM students 
               WHERE current_group_id = ? AND is_active = 1
               ORDER BY full_name''',
            (self.id,)
        )
        return results
    
    def get_students_count(self):
        """Get number of students in this group"""
        result = db_manager.execute_query(
            "SELECT COUNT(*) FROM students WHERE current_group_id = ? AND is_active = 1",
            (self.id,)
        )
        return result[0][0] if result else 0
    
    def is_full(self):
        """Check if group is at maximum capacity"""
        return self.get_students_count() >= self.max_students
    
    def add_student(self, student_id):
        """Add a student to this group"""
        if self.is_full():
            raise ValueError("Group is at maximum capacity")
        
        db_manager.execute_update(
            "UPDATE students SET current_group_id = ?, current_level = ? WHERE id = ?",
            (self.id, self.level, student_id)
        )
    
    def remove_student(self, student_id):
        """Remove a student from this group"""
        db_manager.execute_update(
            "UPDATE students SET current_group_id = NULL WHERE id = ? AND current_group_id = ?",
            (student_id, self.id)
        )
    
    def assign_teacher(self, teacher_id):
        """Assign a teacher to this group"""
        self.update(teacher_id=teacher_id)
    
    def unassign_teacher(self):
        """Remove teacher assignment from this group"""
        self.update(teacher_id=None)
    
    def get_attendance_summary(self, start_date=None, end_date=None):
        """Get attendance summary for this group"""
        base_query = '''
            SELECT 
                COUNT(DISTINCT a.session_date) as total_sessions,
                COUNT(DISTINCT a.student_id) as total_students,
                COUNT(*) as total_records,
                SUM(CASE WHEN a.is_present = 1 THEN 1 ELSE 0 END) as present_records
            FROM attendance a
            WHERE a.group_id = ?
        '''
        
        params = [self.id]
        
        if start_date:
            base_query += " AND a.session_date >= ?"
            params.append(start_date)
        
        if end_date:
            base_query += " AND a.session_date <= ?"
            params.append(end_date)
        
        result = db_manager.execute_query(base_query, params)
        
        if result and result[0][0] > 0:
            total_sessions, total_students, total_records, present_records = result[0]
            return {
                'total_sessions': total_sessions,
                'total_students': total_students,
                'attendance_rate': (present_records / total_records * 100) if total_records > 0 else 0,
                'average_attendance_per_session': present_records / total_sessions if total_sessions > 0 else 0
            }
        
        return {
            'total_sessions': 0,
            'total_students': 0,
            'attendance_rate': 0,
            'average_attendance_per_session': 0
        }
    
    def get_course_info(self):
        """Get course information for this group"""
        result = db_manager.execute_query(
            "SELECT * FROM courses WHERE id = ?", (self.course_id,)
        )
        return result[0] if result else None
    
    def get_teacher_info(self):
        """Get teacher information for this group"""
        if not self.teacher_id:
            return None
        
        result = db_manager.execute_query(
            "SELECT * FROM teachers WHERE id = ?", (self.teacher_id,)
        )
        return result[0] if result else None
    
    def create_session_attendance(self, session_date, session_number):
        """Create attendance records for all students in the group for a session"""
        students = self.get_students()
        
        for student in students:
            # Create attendance record with default absent status
            db_manager.execute_update(
                '''INSERT OR IGNORE INTO attendance 
                   (student_id, group_id, session_date, session_number, is_present)
                   VALUES (?, ?, ?, ?, 0)''',
                (student[0], self.id, session_date, session_number)  # student[0] is student.id
            )
    
    def deactivate(self):
        """Deactivate group"""
        self.is_active = False
        db_manager.execute_update(
            "UPDATE groups SET is_active = 0 WHERE id = ?", (self.id,)
        )
        
        # Remove students from this group
        db_manager.execute_update(
            "UPDATE students SET current_group_id = NULL WHERE current_group_id = ?", (self.id,)
        )
    
    def to_dict(self):
        """Convert group to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'course_id': self.course_id,
            'level': self.level,
            'teacher_id': self.teacher_id,
            'max_students': self.max_students,
            'created_at': self.created_at,
            'is_active': self.is_active
        }
