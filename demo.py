#!/usr/bin/env python3
"""
Demo script for Educational Academy Management System
This script demonstrates the core functionality without the GUI
"""

import sys
import os
from datetime import datetime, date

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from config.settings import app_settings
from models.user import User
from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.attendance import Attendance
from models.financial import FinancialTransaction

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_section(title):
    """Print a formatted section header"""
    print(f"\n--- {title} ---")

def demo_user_management():
    """Demonstrate user management functionality"""
    print_header("USER MANAGEMENT DEMO")
    
    # Show default admin user
    admin = User.get_by_username("admin")
    print(f"Default Admin User: {admin.full_name} ({admin.role})")
    
    # Create additional users
    print_section("Creating Additional Users")
    
    try:
        staff_user = User.create("staff1", "password123", "staff", "<PERSON>")
        print(f"✓ Created staff user: {staff_user.full_name}")
    except:
        staff_user = User.get_by_username("staff1")
        print(f"✓ Staff user already exists: {staff_user.full_name}")
    
    try:
        accountant = User.create("accountant1", "password123", "accountant", "Bob Wilson")
        print(f"✓ Created accountant: {accountant.full_name}")
    except:
        accountant = User.get_by_username("accountant1")
        print(f"✓ Accountant already exists: {accountant.full_name}")
    
    # Show all users
    print_section("All Users")
    users = User.get_all()
    for user in users:
        print(f"- {user.full_name} ({user.username}) - Role: {user.role}")

def demo_course_management():
    """Demonstrate course management functionality"""
    print_header("COURSE MANAGEMENT DEMO")
    
    # Create sample courses
    courses_data = [
        ("Mathematics", "Basic to advanced mathematics", 4, 15, 150.0),
        ("English", "English language and literature", 3, 12, 120.0),
        ("Science", "General science course", 3, 14, 140.0),
        ("Computer Science", "Programming and computer basics", 5, 16, 200.0)
    ]
    
    print_section("Creating Courses")
    created_courses = []
    
    for name, desc, levels, sessions, fee in courses_data:
        try:
            course = Course.create(name, desc, levels, sessions, fee)
            created_courses.append(course)
            print(f"✓ Created course: {name} ({levels} levels, ${fee}/level)")
        except:
            # Course might already exist
            courses = Course.get_all()
            for course in courses:
                if course.name == name:
                    created_courses.append(course)
                    print(f"✓ Course already exists: {name}")
                    break
    
    # Show all courses
    print_section("All Courses")
    all_courses = Course.get_all()
    for course in all_courses:
        print(f"- {course.name}: {course.total_levels} levels, {course.sessions_per_level} sessions/level, ${course.fee_per_level}/level")
    
    return created_courses

def demo_teacher_management():
    """Demonstrate teacher management functionality"""
    print_header("TEACHER MANAGEMENT DEMO")
    
    # Create sample teachers
    teachers_data = [
        ("T001", "Dr. Sarah Johnson", "Mathematics", "555-0101", "<EMAIL>", 4000.0),
        ("T002", "Prof. Michael Brown", "English", "555-0102", "<EMAIL>", 3800.0),
        ("T003", "Ms. Lisa Davis", "Science", "555-0103", "<EMAIL>", 3600.0),
        ("T004", "Mr. David Wilson", "Computer Science", "555-0104", "<EMAIL>", 4200.0)
    ]
    
    print_section("Creating Teachers")
    created_teachers = []
    
    for teacher_id, name, spec, phone, email, salary in teachers_data:
        try:
            teacher = Teacher.create(teacher_id, name, spec, phone, email, salary)
            created_teachers.append(teacher)
            print(f"✓ Created teacher: {name} ({spec}) - ${salary}/month")
        except:
            # Teacher might already exist
            teacher = Teacher.get_by_teacher_id(teacher_id)
            if teacher:
                created_teachers.append(teacher)
                print(f"✓ Teacher already exists: {name}")
    
    # Show all teachers
    print_section("All Teachers")
    all_teachers = Teacher.get_all()
    for teacher in all_teachers:
        print(f"- {teacher.full_name} ({teacher.teacher_id}): {teacher.specialization} - ${teacher.salary}/month")
    
    return created_teachers

def demo_group_management(courses, teachers):
    """Demonstrate group management functionality"""
    print_header("GROUP MANAGEMENT DEMO")
    
    print_section("Creating Groups")
    created_groups = []
    
    # Create groups for each course
    for i, course in enumerate(courses[:4]):  # Limit to first 4 courses
        teacher = teachers[i] if i < len(teachers) else None
        
        try:
            group = Group.create(
                name=f"{course.name} - Level 1 Group A",
                course_id=course.id,
                level=1,
                teacher_id=teacher.id if teacher else None,
                max_students=20
            )
            created_groups.append(group)
            teacher_name = teacher.full_name if teacher else "No Teacher"
            print(f"✓ Created group: {group.name} (Teacher: {teacher_name})")
        except Exception as e:
            print(f"✗ Failed to create group for {course.name}: {e}")
    
    # Show all groups
    print_section("All Groups")
    all_groups = Group.get_all()
    for group_data in all_groups:
        group_name = group_data[1]
        course_name = group_data[6] if len(group_data) > 6 else "Unknown"
        teacher_name = group_data[7] if len(group_data) > 7 and group_data[7] else "No Teacher"
        print(f"- {group_name} ({course_name}) - Teacher: {teacher_name}")
    
    return created_groups

def demo_student_management(groups):
    """Demonstrate student management functionality"""
    print_header("STUDENT MANAGEMENT DEMO")
    
    # Create sample students
    students_data = [
        ("S001", "Alice Johnson", "555-1001", "123 Oak St, Springfield"),
        ("S002", "Bob Smith", "555-1002", "456 Pine Ave, Springfield"),
        ("S003", "Carol Davis", "555-1003", "789 Elm St, Springfield"),
        ("S004", "David Wilson", "555-1004", "321 Maple Dr, Springfield"),
        ("S005", "Emma Brown", "555-1005", "654 Cedar Ln, Springfield"),
        ("S006", "Frank Miller", "555-1006", "987 Birch Rd, Springfield"),
        ("S007", "Grace Taylor", "555-1007", "147 Spruce St, Springfield"),
        ("S008", "Henry Anderson", "555-1008", "258 Willow Ave, Springfield")
    ]
    
    print_section("Creating Students")
    created_students = []
    
    for i, (student_id, name, phone, address) in enumerate(students_data):
        # Assign students to groups in round-robin fashion
        group = groups[i % len(groups)] if groups else None
        
        try:
            student = Student.create(
                student_id=student_id,
                full_name=name,
                parent_phone=phone,
                address=address,
                current_group_id=group.id if group else None
            )
            created_students.append(student)
            group_name = group.name if group else "No Group"
            print(f"✓ Created student: {name} ({student_id}) - Group: {group_name}")
        except:
            # Student might already exist
            student = Student.get_by_student_id(student_id)
            if student:
                created_students.append(student)
                print(f"✓ Student already exists: {name}")
    
    # Show all students
    print_section("All Students")
    all_students = Student.get_all()
    for student in all_students:
        group_name = "No Group"
        if student.current_group_id:
            group = Group.get_by_id(student.current_group_id)
            if group:
                group_name = group.name
        
        print(f"- {student.full_name} ({student.student_id}): {group_name} - Level {student.current_level}")
    
    return created_students

def demo_attendance_management(students, groups):
    """Demonstrate attendance management functionality"""
    print_header("ATTENDANCE MANAGEMENT DEMO")
    
    if not students or not groups:
        print("No students or groups available for attendance demo")
        return
    
    print_section("Recording Sample Attendance")
    
    # Record attendance for the past few days
    from datetime import timedelta
    
    today = date.today()
    
    for days_ago in range(5, 0, -1):  # Last 5 days
        session_date = today - timedelta(days=days_ago)
        
        print(f"\nRecording attendance for {session_date}:")
        
        for group in groups[:2]:  # First 2 groups
            group_students = Student.get_by_group(group.id)
            
            if group_students:
                # Create session attendance
                Attendance.create_session_for_group(group.id, session_date, days_ago)
                
                # Mark some students present (simulate realistic attendance)
                import random
                for student in group_students:
                    # 85% attendance rate simulation
                    is_present = random.random() < 0.85
                    
                    Attendance.record_attendance(
                        student.id, group.id, session_date, days_ago, is_present
                    )
                
                present_count = len([s for s in group_students if random.random() < 0.85])
                print(f"  {group.name}: {present_count}/{len(group_students)} students present")
    
    # Show attendance summary
    print_section("Attendance Summary")
    for student in students[:5]:  # First 5 students
        summary = student.get_attendance_summary()
        print(f"- {student.full_name}: {summary['attendance_rate']:.1f}% attendance "
              f"({summary['present_sessions']}/{summary['total_sessions']} sessions)")

def demo_financial_management(students, teachers):
    """Demonstrate financial management functionality"""
    print_header("FINANCIAL MANAGEMENT DEMO")
    
    print_section("Recording Sample Transactions")
    
    # Record course fee payments
    for i, student in enumerate(students[:6]):  # First 6 students
        amount = 150.0  # Course fee
        FinancialTransaction.record_course_payment(
            student.id, amount, "Mathematics", f"Course fee payment from {student.full_name}"
        )
        print(f"✓ Recorded course fee payment: {student.full_name} - ${amount}")
    
    # Record teacher salary payments
    for teacher in teachers[:3]:  # First 3 teachers
        FinancialTransaction.record_salary_payment(
            teacher.id, teacher.salary, f"Monthly salary for {teacher.full_name}"
        )
        print(f"✓ Recorded salary payment: {teacher.full_name} - ${teacher.salary}")
    
    # Record other expenses
    expenses = [
        ("rent", 2000.0, "Monthly office rent"),
        ("utilities", 300.0, "Electricity and water bills"),
        ("supplies", 150.0, "Teaching materials and supplies"),
        ("maintenance", 200.0, "Equipment maintenance")
    ]
    
    for category, amount, description in expenses:
        FinancialTransaction.record_expense(category, amount, description)
        print(f"✓ Recorded expense: {description} - ${amount}")
    
    # Show financial summary
    print_section("Financial Summary")
    profit_loss = FinancialTransaction.get_profit_loss()
    
    print(f"Total Income: ${profit_loss['income']:.2f}")
    print(f"Total Expenses: ${profit_loss['expenses']:.2f}")
    print(f"Profit/Loss: ${profit_loss['profit_loss']:.2f}")
    
    if profit_loss['profit_loss'] >= 0:
        print("✓ Academy is profitable!")
    else:
        print("⚠ Academy is operating at a loss")

def demo_reports():
    """Demonstrate report generation capabilities"""
    print_header("REPORTS DEMO")
    
    print_section("Available Report Types")
    print("✓ Student Reports (Individual and List)")
    print("✓ Attendance Reports (Group and Individual)")
    print("✓ Financial Reports (Income, Expenses, Profit/Loss)")
    print("✓ Teacher Reports (Performance and Salary)")
    print("✓ Course Reports (Enrollment and Revenue)")
    
    print_section("Export Formats")
    print("✓ PDF Reports (using ReportLab)")
    print("✓ Excel Spreadsheets (using openpyxl)")
    print("✓ Database Backups (SQLite with metadata)")
    
    print("\nNote: Report generation functionality is implemented in utils/ directory")
    print("- utils/pdf_generator.py - PDF report generation")
    print("- utils/excel_exporter.py - Excel export functionality")
    print("- utils/backup_manager.py - Database backup and restore")

def main():
    """Run the complete demo"""
    print_header("EDUCATIONAL ACADEMY MANAGEMENT SYSTEM - DEMO")
    print(f"Academy Name: {app_settings.get_academy_name()}")
    print(f"Demo Date: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
    
    # Initialize database
    try:
        db_manager.init_database()
        print("✓ Database initialized successfully")
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return
    
    # Run demos
    demo_user_management()
    
    courses = demo_course_management()
    teachers = demo_teacher_management()
    groups = demo_group_management(courses, teachers)
    students = demo_student_management(groups)
    
    demo_attendance_management(students, groups)
    demo_financial_management(students, teachers)
    demo_reports()
    
    print_header("DEMO COMPLETED SUCCESSFULLY")
    print("✓ All core functionality demonstrated")
    print("✓ Database populated with sample data")
    print("✓ Ready for GUI application testing")
    print("\nTo run the GUI application:")
    print("  python main.py")
    print("\nDefault login credentials:")
    print("  Username: admin")
    print("  Password: admin123")

if __name__ == "__main__":
    main()
