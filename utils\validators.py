import re
from datetime import datetime

class Validators:
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        if not email:
            return True  # Email is optional
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number format"""
        if not phone:
            return True  # Phone is optional
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Check if it has at least 10 digits
        return len(digits_only) >= 10
    
    @staticmethod
    def validate_student_id(student_id):
        """Validate student ID format"""
        if not student_id:
            return False, "Student ID is required"
        
        # Remove whitespace
        student_id = student_id.strip()
        
        if len(student_id) < 3:
            return False, "Student ID must be at least 3 characters long"
        
        if len(student_id) > 20:
            return False, "Student ID must be less than 20 characters"
        
        # Allow alphanumeric characters, hyphens, and underscores
        if not re.match(r'^[a-zA-Z0-9_-]+$', student_id):
            return False, "Student ID can only contain letters, numbers, hyphens, and underscores"
        
        return True, "Valid"
    
    @staticmethod
    def validate_teacher_id(teacher_id):
        """Validate teacher ID format"""
        if not teacher_id:
            return False, "Teacher ID is required"
        
        # Remove whitespace
        teacher_id = teacher_id.strip()
        
        if len(teacher_id) < 3:
            return False, "Teacher ID must be at least 3 characters long"
        
        if len(teacher_id) > 20:
            return False, "Teacher ID must be less than 20 characters"
        
        # Allow alphanumeric characters, hyphens, and underscores
        if not re.match(r'^[a-zA-Z0-9_-]+$', teacher_id):
            return False, "Teacher ID can only contain letters, numbers, hyphens, and underscores"
        
        return True, "Valid"
    
    @staticmethod
    def validate_name(name, field_name="Name"):
        """Validate name fields"""
        if not name:
            return False, f"{field_name} is required"
        
        name = name.strip()
        
        if len(name) < 2:
            return False, f"{field_name} must be at least 2 characters long"
        
        if len(name) > 100:
            return False, f"{field_name} must be less than 100 characters"
        
        # Allow letters, spaces, hyphens, and apostrophes
        if not re.match(r"^[a-zA-Z\s\-']+$", name):
            return False, f"{field_name} can only contain letters, spaces, hyphens, and apostrophes"
        
        return True, "Valid"
    
    @staticmethod
    def validate_username(username):
        """Validate username format"""
        if not username:
            return False, "Username is required"
        
        username = username.strip()
        
        if len(username) < 3:
            return False, "Username must be at least 3 characters long"
        
        if len(username) > 50:
            return False, "Username must be less than 50 characters"
        
        # Allow alphanumeric characters and underscores
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "Username can only contain letters, numbers, and underscores"
        
        return True, "Valid"
    
    @staticmethod
    def validate_password(password):
        """Validate password strength"""
        if not password:
            return False, "Password is required"
        
        if len(password) < 6:
            return False, "Password must be at least 6 characters long"
        
        if len(password) > 100:
            return False, "Password must be less than 100 characters"
        
        return True, "Valid"
    
    @staticmethod
    def validate_amount(amount_str):
        """Validate monetary amount"""
        if not amount_str:
            return False, "Amount is required"
        
        try:
            amount = float(amount_str)
            if amount < 0:
                return False, "Amount cannot be negative"
            if amount > 999999999:
                return False, "Amount is too large"
            return True, "Valid"
        except ValueError:
            return False, "Amount must be a valid number"
    
    @staticmethod
    def validate_integer(value_str, field_name="Value", min_value=None, max_value=None):
        """Validate integer values"""
        if not value_str:
            return False, f"{field_name} is required"
        
        try:
            value = int(value_str)
            
            if min_value is not None and value < min_value:
                return False, f"{field_name} must be at least {min_value}"
            
            if max_value is not None and value > max_value:
                return False, f"{field_name} must be at most {max_value}"
            
            return True, "Valid"
        except ValueError:
            return False, f"{field_name} must be a valid integer"
    
    @staticmethod
    def validate_date(date_str):
        """Validate date format (YYYY-MM-DD)"""
        if not date_str:
            return False, "Date is required"
        
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True, "Valid"
        except ValueError:
            return False, "Date must be in YYYY-MM-DD format"
    
    @staticmethod
    def validate_course_name(name):
        """Validate course name"""
        if not name:
            return False, "Course name is required"
        
        name = name.strip()
        
        if len(name) < 2:
            return False, "Course name must be at least 2 characters long"
        
        if len(name) > 100:
            return False, "Course name must be less than 100 characters"
        
        return True, "Valid"
    
    @staticmethod
    def validate_group_name(name):
        """Validate group name"""
        if not name:
            return False, "Group name is required"
        
        name = name.strip()
        
        if len(name) < 1:
            return False, "Group name is required"
        
        if len(name) > 50:
            return False, "Group name must be less than 50 characters"
        
        return True, "Valid"
    
    @staticmethod
    def sanitize_filename(filename):
        """Sanitize filename for safe storage"""
        # Remove or replace invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Limit length
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:255-len(ext)-1] + '.' + ext if ext else name[:255]
        
        return filename
    
    @staticmethod
    def validate_file_extension(filename, allowed_extensions):
        """Validate file extension"""
        if not filename:
            return False, "Filename is required"
        
        if '.' not in filename:
            return False, "File must have an extension"
        
        extension = filename.lower().split('.')[-1]
        
        if extension not in [ext.lower() for ext in allowed_extensions]:
            return False, f"File must be one of: {', '.join(allowed_extensions)}"
        
        return True, "Valid"
