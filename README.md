# Educational Academy Management System

A comprehensive desktop application for managing educational academies built with Python, CustomTkinter, and SQLite.

## Features

### ✅ Implemented Features

#### 🔐 User Management & Authentication
- Secure login system with role-based permissions
- Three user roles: Admin, Staff, and Accountant
- Password hashing with bcrypt
- Default admin account (username: `admin`, password: `admin123`)

#### 👥 Student Management System
- Add new students with complete profiles
- Student ID, full name, parent phone, address
- Profile photo support (placeholder implemented)
- Group assignments and level tracking
- Search functionality by name or student ID
- Edit and delete student records
- Student transfer between groups

#### 📊 Dashboard
- Real-time statistics display
- Total students, teachers, courses count
- Monthly profit/loss overview
- Quick action buttons for common tasks

#### 🏗️ Database Architecture
- SQLite database with proper relationships
- Comprehensive schema for all entities
- Data validation and error handling
- Automatic database initialization

### 🚧 Features In Development

#### 👨‍🏫 Teacher Management System
- Register teachers with specializations
- Assign teachers to groups and courses
- Salary management and payment tracking

#### 📚 Course Management System
- Create courses with configurable levels
- Organize students into groups within levels
- Define sessions per level

#### ✅ Attendance Tracking System
- Record attendance using student ID
- Automatic absence marking
- Generate attendance reports
- Session management

#### 💰 Financial Management System
- Track course fees (paid/unpaid)
- Manage expenses (rent, salaries, supplies)
- Generate profit/loss reports
- Export financial reports as PDF/Excel

#### ⚙️ Settings & Customization
- Theme customization
- Academy logo upload
- Currency settings
- System preferences

## Installation

### Prerequisites
- Python 3.8 or higher
- Windows, macOS, or Linux

### Setup Instructions

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd CL4
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python main.py
   ```

## Usage

### First Login
- **Username:** `admin`
- **Password:** `admin123`

### Navigation
- Use the sidebar to navigate between different modules
- Dashboard provides an overview of academy statistics
- Each module has its own management interface

### Student Management
1. Click "👥 Students" in the sidebar
2. Use "Add Student" button to register new students
3. Search students using the search bar
4. Select a student to view/edit details
5. Right-click on students for context menu options

### User Roles & Permissions
- **Admin:** Full access to all features
- **Staff:** Access to students, attendance, courses, and teachers
- **Accountant:** Access to financial management and student records

## Project Structure

```
CL4/
├── main.py                     # Application entry point
├── requirements.txt            # Dependencies
├── config/
│   ├── database.py            # Database configuration
│   └── settings.py            # Application settings
├── models/
│   ├── user.py               # User model
│   ├── student.py            # Student model
│   ├── teacher.py            # Teacher model
│   ├── course.py             # Course model
│   ├── group.py              # Group model
│   ├── attendance.py         # Attendance model
│   └── financial.py          # Financial model
├── views/
│   ├── login_window.py       # Login interface
│   ├── main_window.py        # Main dashboard
│   └── student_management.py # Student management interface
├── utils/
│   └── validators.py         # Data validation utilities
├── assets/
│   └── images/              # UI images and icons
└── data/
    └── academy.db           # SQLite database (auto-created)
```

## Database Schema

The application uses SQLite with the following main tables:
- `users` - User accounts and roles
- `students` - Student information and profiles
- `teachers` - Teacher information and specializations
- `courses` - Course definitions and levels
- `groups` - Student groups within courses
- `attendance` - Attendance records
- `financial_transactions` - Income and expense tracking
- `settings` - Application configuration

## Development

### Adding New Features
1. Create model classes in `models/` directory
2. Implement views in `views/` directory
3. Add controllers in `controllers/` directory (if needed)
4. Update navigation in `main_window.py`

### Database Migrations
The database schema is automatically created on first run. For schema changes:
1. Update the model classes
2. Modify `database.py` initialization
3. Handle data migration if needed

## Security Features
- Password hashing with bcrypt
- Role-based access control
- Input validation and sanitization
- SQL injection prevention through parameterized queries

## Backup & Recovery
- Database backup functionality (to be implemented)
- Export/import capabilities (to be implemented)
- Data integrity checks

## Troubleshooting

### Common Issues
1. **Database errors:** Ensure the `data/` directory is writable
2. **Import errors:** Verify all dependencies are installed
3. **UI issues:** Check CustomTkinter version compatibility

### Error Logs
- Application errors are displayed in message boxes
- Console output provides additional debugging information

## Contributing
1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Test thoroughly
5. Submit a pull request

## License
This project is open source and available under the MIT License.

## Support
For support and questions, please create an issue in the repository.

---

**Version:** 1.0.0  
**Author:** Augment Agent  
**Last Updated:** December 2024
