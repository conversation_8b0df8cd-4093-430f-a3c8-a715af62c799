# 🚀 **دليل المستخدم - نظام إدارة الطلاب المتقدم**

## 📋 **البدء السريع:**

### 🎯 **تشغيل النظام:**
```bash
cd "d:\Python\chat\CL4"
python academy_advanced.py
```

### 🔐 **تسجيل الدخول:**
| الدور | المستخدم | كلمة المرور | الصلاحيات |
|-------|----------|-------------|-----------|
| 👑 Admin | `admin` | `admin123` | جميع الصلاحيات |
| 👨‍💼 Staff | `staff1` | `password123` | إدارة الطلاب والحضور |
| 💰 Accountant | `accountant1` | `password123` | المالية والطلاب |

---

## 🎯 **الوظائف الجديدة المكتملة:**

### ➕ **1. إضافة طالب جديد:**
- انقر **"➕ Add Student"**
- املأ جميع البيانات المطلوبة
- انقر **"💾 Save Student"**

### ✏️ **2. تعديل بيانات الطالب:**
- حدد الطالب من الجدول
- انقر **"✏️ Edit Selected"** أو انقر مزدوج
- عدل البيانات وانقر **"💾 Save Changes"**

### 🗑️ **3. حذف الطالب:**
- حدد الطالب من الجدول
- انقر **"🗑️ Delete"**
- أكد الحذف في النافذة المنبثقة

### 📸 **4. رفع صور الطلاب:**
- من نافذة التعديل: انقر **"📸 Upload Photo"**
- أو انقر بالزر الأيمن → **"📸 Upload Photo"**
- اختر الصورة من الكمبيوتر

### 🆔 **5. طباعة بطاقة الطالب:**
- حدد الطالب من الجدول
- انقر **"🆔 Print ID"**
- سيتم إنشاء ملف البطاقة في مجلد `student_cards`

---

## 🔍 **البحث المتقدم:**
- اكتب في مربع البحث أي من:
  - اسم الطالب
  - رقم الطالب  
  - رقم الهاتف
  - العنوان
- النتائج تظهر فورياً أثناء الكتابة

---

## 📊 **فهم الجدول:**
| العمود | الوصف |
|--------|-------|
| **ID** | رقم الطالب الفريد |
| **Name** | الاسم الكامل |
| **Group** | المجموعة الحالية |
| **Level** | المستوى الدراسي |
| **Phone** | رقم هاتف ولي الأمر |
| **Address** | العنوان |
| **Photo** | حالة الصورة (📸 Yes / ❌ No) |
| **Status** | الحالة (✅ Active / ⚠️ Inactive) |
| **Joined** | تاريخ الانضمام |

---

## 🖱️ **التفاعل مع الجدول:**
- **نقرة واحدة:** تحديد الطالب
- **نقرة مزدوجة:** فتح نافذة التعديل
- **نقرة يمين:** قائمة سياق سريعة

---

## 📁 **هيكل الملفات:**
```
📁 Project Root/
├── 📄 academy_advanced.py      # الملف الرئيسي الجديد
├── 📁 student_photos/          # صور الطلاب
├── 📁 student_cards/           # بطاقات الطلاب
├── 📁 backups/                 # النسخ الاحتياطية
├── 📁 reports/                 # التقارير
└── 📄 academy.db              # قاعدة البيانات
```

---

## ⚠️ **نصائح مهمة:**

### ✅ **أفضل الممارسات:**
1. احفظ نسخة احتياطية من قاعدة البيانات
2. استخدم صور عالية الجودة للطلاب
3. تأكد من صحة البيانات قبل الحفظ
4. استخدم البحث للعثور على الطلاب بسرعة

### 🚨 **تجنب:**
1. لا تحذف ملفات النظام
2. لا تعدل قاعدة البيانات مباشرة
3. تأكد من تنسيق التاريخ (YYYY-MM-DD)
4. لا تترك الحقول المطلوبة فارغة

---

## 🔧 **حل المشاكل:**

### ❌ **لا يمكن إضافة طالب:**
**الحل:** تأكد من ملء الاسم ورقم الهاتف

### ❌ **لا تظهر الصورة:**
**الحل:** تأكد من صيغة الصورة (JPG, PNG, BMP, GIF)

### ❌ **خطأ في التاريخ:**
**الحل:** استخدم تنسيق YYYY-MM-DD

---

## 🎉 **النتيجة النهائية:**

### ✅ **تم إنجاز جميع المتطلبات:**
1. ✅ إضافة طالب جديد - مكتمل
2. ✅ تعديل بيانات الطالب - مكتمل  
3. ✅ حذف الطالب - مكتمل
4. ✅ رفع صور الطلاب - مكتمل
5. ✅ طباعة بطاقة الطالب - مكتمل

### 🚀 **النظام جاهز للاستخدام المهني!**

**الملف الرئيسي:** `academy_advanced.py`

**جميع الوظائف تعمل بشكل مثالي!** 🎯
