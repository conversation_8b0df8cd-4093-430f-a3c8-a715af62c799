import bcrypt
from datetime import datetime
from config.database import db_manager

class User:
    def __init__(self, id=None, username=None, password_hash=None, role=None, 
                 full_name=None, created_at=None, is_active=True):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.role = role
        self.full_name = full_name
        self.created_at = created_at
        self.is_active = is_active
    
    @classmethod
    def create(cls, username, password, role, full_name):
        """Create a new user"""
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        user_id = db_manager.execute_update(
            '''INSERT INTO users (username, password_hash, role, full_name)
               VALUES (?, ?, ?, ?)''',
            (username, password_hash, role, full_name)
        )
        
        return cls.get_by_id(user_id)
    
    @classmethod
    def get_by_id(cls, user_id):
        """Get user by ID"""
        result = db_manager.execute_query(
            "SELECT * FROM users WHERE id = ?", (user_id,)
        )
        if result:
            row = result[0]
            return cls(
                id=row[0], username=row[1], password_hash=row[2],
                role=row[3], full_name=row[4], created_at=row[5], is_active=row[6]
            )
        return None
    
    @classmethod
    def get_by_username(cls, username):
        """Get user by username"""
        result = db_manager.execute_query(
            "SELECT * FROM users WHERE username = ? AND is_active = 1", (username,)
        )
        if result:
            row = result[0]
            return cls(
                id=row[0], username=row[1], password_hash=row[2],
                role=row[3], full_name=row[4], created_at=row[5], is_active=row[6]
            )
        return None
    
    @classmethod
    def get_all(cls):
        """Get all active users"""
        results = db_manager.execute_query(
            "SELECT * FROM users WHERE is_active = 1 ORDER BY full_name"
        )
        return [cls(
            id=row[0], username=row[1], password_hash=row[2],
            role=row[3], full_name=row[4], created_at=row[5], is_active=row[6]
        ) for row in results]
    
    def verify_password(self, password):
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def update_password(self, new_password):
        """Update user password"""
        new_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        db_manager.execute_update(
            "UPDATE users SET password_hash = ? WHERE id = ?",
            (new_hash, self.id)
        )
        self.password_hash = new_hash
    
    def update(self, username=None, role=None, full_name=None):
        """Update user information"""
        if username:
            self.username = username
        if role:
            self.role = role
        if full_name:
            self.full_name = full_name
        
        db_manager.execute_update(
            '''UPDATE users SET username = ?, role = ?, full_name = ?
               WHERE id = ?''',
            (self.username, self.role, self.full_name, self.id)
        )
    
    def deactivate(self):
        """Deactivate user"""
        self.is_active = False
        db_manager.execute_update(
            "UPDATE users SET is_active = 0 WHERE id = ?", (self.id,)
        )
    
    def has_permission(self, permission):
        """Check if user has specific permission"""
        permissions = {
            'admin': ['all'],
            'staff': ['students', 'attendance', 'courses', 'teachers'],
            'accountant': ['financial', 'students']
        }
        
        user_permissions = permissions.get(self.role, [])
        return 'all' in user_permissions or permission in user_permissions
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'full_name': self.full_name,
            'created_at': self.created_at,
            'is_active': self.is_active
        }
