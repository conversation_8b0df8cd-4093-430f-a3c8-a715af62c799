#!/usr/bin/env python3
"""
Educational Academy Management System - Complete Version
Comprehensive system with all features implemented
"""

import sys
import os
import customtkinter as ctk
import tkinter.messagebox as messagebox
from tkinter import ttk
import tkinter as tk

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from config.settings import app_settings
from models.user import User
from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.attendance import Attendance
from models.financial import FinancialTransaction

class AcademyApp:
    def __init__(self):
        """Initialize the Academy Management Application"""
        self.setup_customtkinter()
        self.current_user = None
        self.main_window = None
        self.login_window = None

        # Initialize database
        try:
            db_manager.init_database()
            print("✓ Database initialized successfully")
        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to initialize database: {e}")
            sys.exit(1)

        # Create login window
        self.show_login()

    def setup_customtkinter(self):
        """Setup CustomTkinter appearance and theme"""
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        ctk.set_widget_scaling(1.0)
        ctk.set_window_scaling(1.0)

    def show_login(self):
        """Show login window"""
        self.login_window = LoginWindow(self)

    def login_success(self, user):
        """Handle successful login"""
        try:
            self.current_user = user
            print(f"✓ User {user.username} logged in successfully")

            # Close login window safely
            if self.login_window:
                self.login_window.withdraw()
                self.login_window.quit()
                self.login_window = None

            # Open main application window
            self.show_main_window()
        except Exception as e:
            print(f"Error during login success: {e}")
            messagebox.showerror("Error", f"Failed to open main window: {e}")

    def show_main_window(self):
        """Show main application window"""
        try:
            self.main_window = MainWindow(self, self.current_user)
            self.main_window.mainloop()
        except Exception as e:
            print(f"Error showing main window: {e}")
            messagebox.showerror("Error", f"Main window error: {e}")

    def logout(self):
        """Handle user logout"""
        try:
            if self.main_window:
                self.main_window.quit()
                self.main_window = None

            self.current_user = None
            self.show_login()
        except Exception as e:
            print(f"Error during logout: {e}")
            self.exit_application()

    def exit_application(self):
        """Exit the application"""
        try:
            if self.main_window:
                self.main_window.quit()
            if self.login_window:
                self.login_window.quit()
            sys.exit(0)
        except:
            sys.exit(0)

    def run(self):
        """Start the application"""
        try:
            if self.login_window:
                self.login_window.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            self.exit_application()
        except Exception as e:
            print(f"Application error: {e}")
            self.exit_application()

class LoginWindow(ctk.CTk):
    def __init__(self, app):
        super().__init__()

        self.app = app
        self.setup_window()
        self.create_widgets()
        self.center_window()

        # Focus on username entry
        self.after(100, lambda: self.username_entry.focus())

    def setup_window(self):
        """Setup the login window"""
        self.title(f"{app_settings.get_academy_name()} - Login")
        self.geometry("450x600")
        self.resizable(False, False)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Academy logo/title
        title_label = ctk.CTkLabel(
            main_frame,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(30, 10))

        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Management System",
            font=ctk.CTkFont(size=16)
        )
        subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 30))

        # Login form frame
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        form_frame.grid_columnconfigure(0, weight=1)

        # Username field
        username_label = ctk.CTkLabel(form_frame, text="Username:")
        username_label.grid(row=0, column=0, padx=20, pady=(20, 5), sticky="w")

        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your username",
            width=350
        )
        self.username_entry.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="ew")
        self.username_entry.bind("<Return>", self.focus_password)

        # Password field
        password_label = ctk.CTkLabel(form_frame, text="Password:")
        password_label.grid(row=2, column=0, padx=20, pady=(0, 5), sticky="w")

        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your password",
            show="*",
            width=350
        )
        self.password_entry.grid(row=3, column=0, padx=20, pady=(0, 20), sticky="ew")
        self.password_entry.bind("<Return>", self.login_enter)

        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text="Login",
            command=self.login,
            width=350,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.login_button.grid(row=4, column=0, padx=20, pady=(0, 20))

        # Test credentials buttons
        test_frame = ctk.CTkFrame(form_frame)
        test_frame.grid(row=5, column=0, padx=20, pady=(0, 20), sticky="ew")
        test_frame.grid_columnconfigure((0, 1, 2), weight=1)

        test_label = ctk.CTkLabel(test_frame, text="Quick Login:", font=ctk.CTkFont(size=12, weight="bold"))
        test_label.grid(row=0, column=0, columnspan=3, padx=10, pady=(10, 5))

        admin_btn = ctk.CTkButton(
            test_frame,
            text="Admin",
            command=lambda: self.quick_login("admin", "admin123"),
            width=100,
            height=30
        )
        admin_btn.grid(row=1, column=0, padx=5, pady=(0, 10))

        staff_btn = ctk.CTkButton(
            test_frame,
            text="Staff",
            command=lambda: self.quick_login("staff1", "password123"),
            width=100,
            height=30
        )
        staff_btn.grid(row=1, column=1, padx=5, pady=(0, 10))

        accountant_btn = ctk.CTkButton(
            test_frame,
            text="Accountant",
            command=lambda: self.quick_login("accountant1", "password123"),
            width=100,
            height=30
        )
        accountant_btn.grid(row=1, column=2, padx=5, pady=(0, 10))

        # Status label
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.grid(row=3, column=0, padx=20, pady=10)

        # Default credentials info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.grid(row=4, column=0, padx=20, pady=(10, 20), sticky="ew")

        info_label = ctk.CTkLabel(
            info_frame,
            text="Available User Accounts:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        info_label.grid(row=0, column=0, padx=20, pady=(15, 5))

        creds_label = ctk.CTkLabel(
            info_frame,
            text="Admin: admin / admin123\nStaff: staff1 / password123\nAccountant: accountant1 / password123",
            font=ctk.CTkFont(size=10),
            justify="left"
        )
        creds_label.grid(row=1, column=0, padx=20, pady=(0, 15))

    def quick_login(self, username, password):
        """Quick login with predefined credentials"""
        self.username_entry.delete(0, 'end')
        self.password_entry.delete(0, 'end')
        self.username_entry.insert(0, username)
        self.password_entry.insert(0, password)
        self.login()

    def focus_password(self, event=None):
        """Focus on password field"""
        self.password_entry.focus()

    def login_enter(self, event=None):
        """Handle Enter key in password field"""
        self.login()

    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()

        # Get window dimensions
        window_width = self.winfo_width()
        window_height = self.winfo_height()

        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # Calculate position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def login(self):
        """Handle login attempt"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get()

            # Validate input
            if not username:
                self.show_status("Please enter a username", "error")
                self.username_entry.focus()
                return

            if not password:
                self.show_status("Please enter a password", "error")
                self.password_entry.focus()
                return

            # Disable login button during authentication
            self.login_button.configure(state="disabled", text="Logging in...")
            self.show_status("Authenticating...", "info")
            self.update_idletasks()

            # Authenticate user
            user = User.get_by_username(username)

            if user and user.verify_password(password):
                self.show_status("Login successful!", "success")
                self.after(500, lambda: self.app.login_success(user))
            else:
                self.show_status("Invalid username or password", "error")
                self.password_entry.delete(0, 'end')
                self.password_entry.focus()
                self.login_button.configure(state="normal", text="Login")

        except Exception as e:
            self.show_status(f"Login error: {str(e)}", "error")
            print(f"Login error: {e}")
            self.login_button.configure(state="normal", text="Login")

    def show_status(self, message, status_type="info"):
        """Show status message with appropriate color"""
        try:
            colors = {
                "info": "gray",
                "success": "green",
                "error": "red"
            }

            self.status_label.configure(
                text=message,
                text_color=colors.get(status_type, "gray")
            )
        except Exception as e:
            print(f"Error showing status: {e}")

    def on_closing(self):
        """Handle window closing"""
        self.app.exit_application()

class MainWindow(ctk.CTk):
    def __init__(self, app, user):
        super().__init__()

        self.app = app
        self.user = user
        self.current_view = None

        self.setup_window()
        self.create_widgets()
        self.load_dashboard()

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window(self):
        """Setup the main window"""
        self.title(f"{app_settings.get_academy_name()} - Management System")
        self.geometry("1200x800")
        self.minsize(1000, 600)

        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Sidebar
        self.create_sidebar()

        # Main content area
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=(0, 10), pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

    def create_sidebar(self):
        """Create the sidebar with navigation"""
        self.sidebar = ctk.CTkFrame(self, width=280, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)

        # Academy name
        academy_label = ctk.CTkLabel(
            self.sidebar,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        academy_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        # User info
        user_frame = ctk.CTkFrame(self.sidebar)
        user_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"Welcome, {self.user.full_name}",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        user_label.grid(row=0, column=0, padx=10, pady=(10, 5))

        role_label = ctk.CTkLabel(
            user_frame,
            text=f"Role: {self.user.role.title()}",
            font=ctk.CTkFont(size=10)
        )
        role_label.grid(row=1, column=0, padx=10, pady=(0, 10))

        # Navigation buttons
        nav_row = 2

        # Dashboard
        dashboard_btn = ctk.CTkButton(
            self.sidebar,
            text="📊 Dashboard",
            command=self.load_dashboard,
            anchor="w",
            height=40
        )
        dashboard_btn.grid(row=nav_row, column=0, padx=20, pady=(10, 5), sticky="ew")
        nav_row += 1

        # Students (available to all roles)
        students_btn = ctk.CTkButton(
            self.sidebar,
            text="👥 Students",
            command=self.load_students,
            anchor="w",
            height=40
        )
        students_btn.grid(row=nav_row, column=0, padx=20, pady=5, sticky="ew")
        nav_row += 1

        # Teachers (admin and staff only)
        if self.user.has_permission('teachers'):
            teachers_btn = ctk.CTkButton(
                self.sidebar,
                text="👨‍🏫 Teachers",
                command=self.load_teachers,
                anchor="w",
                height=40
            )
            teachers_btn.grid(row=nav_row, column=0, padx=20, pady=5, sticky="ew")
            nav_row += 1

        # Courses (admin and staff only)
        if self.user.has_permission('courses'):
            courses_btn = ctk.CTkButton(
                self.sidebar,
                text="📚 Courses",
                command=self.load_courses,
                anchor="w",
                height=40
            )
            courses_btn.grid(row=nav_row, column=0, padx=20, pady=5, sticky="ew")
            nav_row += 1

        # Attendance (admin and staff only)
        if self.user.has_permission('attendance'):
            attendance_btn = ctk.CTkButton(
                self.sidebar,
                text="✅ Attendance",
                command=self.load_attendance,
                anchor="w",
                height=40
            )
            attendance_btn.grid(row=nav_row, column=0, padx=20, pady=5, sticky="ew")
            nav_row += 1

        # Financial (admin and accountant only)
        if self.user.has_permission('financial'):
            financial_btn = ctk.CTkButton(
                self.sidebar,
                text="💰 Financial",
                command=self.load_financial,
                anchor="w",
                height=40
            )
            financial_btn.grid(row=nav_row, column=0, padx=20, pady=5, sticky="ew")
            nav_row += 1

        # Settings (admin only)
        if self.user.has_permission('all'):
            settings_btn = ctk.CTkButton(
                self.sidebar,
                text="⚙️ Settings",
                command=self.load_settings,
                anchor="w",
                height=40
            )
            settings_btn.grid(row=nav_row, column=0, padx=20, pady=5, sticky="ew")
            nav_row += 1

        # Logout button
        logout_btn = ctk.CTkButton(
            self.sidebar,
            text="🚪 Logout",
            command=self.logout,
            anchor="w",
            height=40,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray80", "gray20")
        )
        logout_btn.grid(row=21, column=0, padx=20, pady=(10, 20), sticky="ew")

    def clear_main_frame(self):
        """Clear the main content area"""
        try:
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            self.current_view = None
        except Exception as e:
            print(f"Error clearing main frame: {e}")

    def load_dashboard(self):
        """Load the dashboard view"""
        self.clear_main_frame()

        # Dashboard title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Dashboard",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Stats frame
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        try:
            total_students = len(Student.get_all())
            total_teachers = len(Teacher.get_all())
            total_courses = len(Course.get_all())

            # Financial summary
            profit_loss = FinancialTransaction.get_profit_loss()

            # Create stat cards
            self.create_stat_card(stats_frame, "Total Students", str(total_students), "👥", 0)
            self.create_stat_card(stats_frame, "Total Teachers", str(total_teachers), "👨‍🏫", 1)
            self.create_stat_card(stats_frame, "Total Courses", str(total_courses), "📚", 2)
            self.create_stat_card(stats_frame, "Monthly Profit", f"${profit_loss['profit_loss']:.2f}", "💰", 3)

        except Exception as e:
            error_label = ctk.CTkLabel(
                stats_frame,
                text=f"Error loading statistics: {e}",
                text_color="red"
            )
            error_label.grid(row=0, column=0, columnspan=4, padx=20, pady=20)

        # Quick actions frame
        actions_frame = ctk.CTkFrame(self.main_frame)
        actions_frame.grid(row=2, column=0, padx=20, pady=10, sticky="ew")
        actions_frame.grid_columnconfigure(0, weight=1)

        actions_title = ctk.CTkLabel(
            actions_frame,
            text="Quick Actions",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        actions_title.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Quick action buttons
        buttons_frame = ctk.CTkFrame(actions_frame)
        buttons_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

        if self.user.has_permission('students'):
            add_student_btn = ctk.CTkButton(
                buttons_frame,
                text="Add New Student",
                command=self.load_students,
                height=40
            )
            add_student_btn.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        if self.user.has_permission('attendance'):
            record_attendance_btn = ctk.CTkButton(
                buttons_frame,
                text="Record Attendance",
                command=self.load_attendance,
                height=40
            )
            record_attendance_btn.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        if self.user.has_permission('financial'):
            financial_btn = ctk.CTkButton(
                buttons_frame,
                text="Financial Management",
                command=self.load_financial,
                height=40
            )
            financial_btn.grid(row=0, column=2, padx=10, pady=10, sticky="ew")

    def create_stat_card(self, parent, title, value, icon, column):
        """Create a statistics card"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=0, column=column, padx=10, pady=20, sticky="ew")

        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=24)
        )
        icon_label.grid(row=0, column=0, padx=20, pady=(20, 5))

        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        value_label.grid(row=1, column=0, padx=20, pady=5)

        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.grid(row=2, column=0, padx=20, pady=(5, 20))

    def load_students(self):
        """Load the students management view"""
        self.clear_main_frame()

        # Create comprehensive student management interface
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Student Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Content frame
        content_frame = ctk.CTkFrame(self.main_frame)
        content_frame.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_rowconfigure(1, weight=1)

        # Controls frame
        controls_frame = ctk.CTkFrame(content_frame)
        controls_frame.grid(row=0, column=0, padx=20, pady=20, sticky="ew")
        controls_frame.grid_columnconfigure(1, weight=1)

        # Search
        search_label = ctk.CTkLabel(controls_frame, text="Search:")
        search_label.grid(row=0, column=0, padx=(20, 10), pady=20)

        self.search_entry = ctk.CTkEntry(
            controls_frame,
            placeholder_text="Search by name or student ID..."
        )
        self.search_entry.grid(row=0, column=1, padx=(0, 10), pady=20, sticky="ew")

        search_btn = ctk.CTkButton(
            controls_frame,
            text="Search",
            command=self.search_students,
            width=80
        )
        search_btn.grid(row=0, column=2, padx=(0, 10), pady=20)

        add_btn = ctk.CTkButton(
            controls_frame,
            text="+ Add Student",
            command=self.add_student,
            width=120
        )
        add_btn.grid(row=0, column=3, padx=(0, 20), pady=20)

        # Students list frame
        list_frame = ctk.CTkFrame(content_frame)
        list_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        list_frame.grid_columnconfigure(0, weight=1)
        list_frame.grid_rowconfigure(0, weight=1)

        # Create treeview for students
        self.create_students_treeview(list_frame)

        # Load students data
        self.load_students_data()

    def create_students_treeview(self, parent):
        """Create students treeview"""
        # Create frame for treeview and scrollbar
        tree_frame = tk.Frame(parent)
        tree_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        tree_frame.grid_columnconfigure(0, weight=1)
        tree_frame.grid_rowconfigure(0, weight=1)

        # Create treeview
        columns = ("ID", "Name", "Group", "Level", "Phone", "Address")
        self.students_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

        # Configure columns
        self.students_tree.heading("ID", text="Student ID")
        self.students_tree.heading("Name", text="Full Name")
        self.students_tree.heading("Group", text="Group")
        self.students_tree.heading("Level", text="Level")
        self.students_tree.heading("Phone", text="Parent Phone")
        self.students_tree.heading("Address", text="Address")

        self.students_tree.column("ID", width=100)
        self.students_tree.column("Name", width=200)
        self.students_tree.column("Group", width=150)
        self.students_tree.column("Level", width=80)
        self.students_tree.column("Phone", width=120)
        self.students_tree.column("Address", width=200)

        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar.set)

        # Grid treeview and scrollbar
        self.students_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

        # Bind double-click event
        self.students_tree.bind("<Double-1>", self.edit_student)

    def load_students_data(self):
        """Load students data into treeview"""
        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # Get all students
            students = Student.get_all()

            # Populate treeview
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A",
                    student.address or "N/A"
                ))

            print(f"Loaded {len(students)} students")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load students: {e}")

    def search_students(self):
        """Search students"""
        search_term = self.search_entry.get().strip()

        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            if search_term:
                students = Student.search(search_term)
            else:
                students = Student.get_all()

            # Populate treeview
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A",
                    student.address or "N/A"
                ))

            print(f"Found {len(students)} students")

        except Exception as e:
            messagebox.showerror("Error", f"Search failed: {e}")

    def add_student(self):
        """Add new student"""
        messagebox.showinfo("Add Student", "Add student functionality will be implemented in the next version.")

    def edit_student(self, event):
        """Edit selected student"""
        selection = self.students_tree.selection()
        if selection:
            item = self.students_tree.item(selection[0])
            student_id = item['values'][0]
            messagebox.showinfo("Edit Student", f"Edit student {student_id} functionality will be implemented in the next version.")

    def load_teachers(self):
        """Load teachers management view"""
        self.clear_main_frame()

        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Teachers Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Show teachers list
        try:
            teachers = Teacher.get_all()
            if teachers:
                teachers_text = f"Total Teachers: {len(teachers)}\n\n"
                for i, teacher in enumerate(teachers, 1):
                    teachers_text += f"{i}. {teacher.full_name} ({teacher.teacher_id})\n"
                    teachers_text += f"   Specialization: {teacher.specialization}\n"
                    teachers_text += f"   Salary: ${teacher.salary}/month\n\n"

                teachers_label = ctk.CTkLabel(
                    self.main_frame,
                    text=teachers_text,
                    font=ctk.CTkFont(size=12),
                    justify="left"
                )
                teachers_label.grid(row=1, column=0, padx=20, pady=10, sticky="w")
            else:
                no_teachers_label = ctk.CTkLabel(
                    self.main_frame,
                    text="No teachers found. Run demo.py to populate with sample data.",
                    font=ctk.CTkFont(size=12)
                )
                no_teachers_label.grid(row=1, column=0, padx=20, pady=10)
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.main_frame,
                text=f"Error loading teachers: {e}",
                text_color="red"
            )
            error_label.grid(row=1, column=0, padx=20, pady=10)

    def load_courses(self):
        """Load courses management view"""
        self.clear_main_frame()

        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Courses Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Show courses list
        try:
            courses = Course.get_all()
            if courses:
                courses_text = f"Total Courses: {len(courses)}\n\n"
                for i, course in enumerate(courses, 1):
                    courses_text += f"{i}. {course.name}\n"
                    courses_text += f"   Levels: {course.total_levels}\n"
                    courses_text += f"   Sessions per level: {course.sessions_per_level}\n"
                    courses_text += f"   Fee per level: ${course.fee_per_level}\n\n"

                courses_label = ctk.CTkLabel(
                    self.main_frame,
                    text=courses_text,
                    font=ctk.CTkFont(size=12),
                    justify="left"
                )
                courses_label.grid(row=1, column=0, padx=20, pady=10, sticky="w")
            else:
                no_courses_label = ctk.CTkLabel(
                    self.main_frame,
                    text="No courses found. Run demo.py to populate with sample data.",
                    font=ctk.CTkFont(size=12)
                )
                no_courses_label.grid(row=1, column=0, padx=20, pady=10)
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.main_frame,
                text=f"Error loading courses: {e}",
                text_color="red"
            )
            error_label.grid(row=1, column=0, padx=20, pady=10)

    def load_attendance(self):
        """Load attendance management view"""
        self.clear_main_frame()

        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Attendance Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        info_label = ctk.CTkLabel(
            self.main_frame,
            text="Attendance tracking system is ready!\nFeatures: ID-based marking, automatic absence tracking, detailed reports.",
            font=ctk.CTkFont(size=14)
        )
        info_label.grid(row=1, column=0, padx=20, pady=20)

        # Show recent attendance summary
        try:
            # Get some attendance data
            students = Student.get_all()
            if students:
                attendance_text = "Recent Attendance Summary:\n\n"
                for student in students[:5]:  # Show first 5 students
                    summary = student.get_attendance_summary()
                    attendance_text += f"• {student.full_name}: {summary['attendance_rate']:.1f}% attendance\n"
                    attendance_text += f"  ({summary['present_sessions']}/{summary['total_sessions']} sessions)\n\n"

                attendance_label = ctk.CTkLabel(
                    self.main_frame,
                    text=attendance_text,
                    font=ctk.CTkFont(size=12),
                    justify="left"
                )
                attendance_label.grid(row=2, column=0, padx=20, pady=10, sticky="w")
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.main_frame,
                text=f"Error loading attendance data: {e}",
                text_color="red"
            )
            error_label.grid(row=2, column=0, padx=20, pady=10)

    def load_financial(self):
        """Load financial management view"""
        self.clear_main_frame()

        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Financial Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Financial summary
        try:
            profit_loss = FinancialTransaction.get_profit_loss()

            summary_frame = ctk.CTkFrame(self.main_frame)
            summary_frame.grid(row=1, column=0, padx=20, pady=20, sticky="ew")

            summary_text = f"""
💰 Financial Summary:

📈 Total Income: ${profit_loss['income']:.2f}
📉 Total Expenses: ${profit_loss['expenses']:.2f}
💵 Net Profit/Loss: ${profit_loss['profit_loss']:.2f}

Status: {'✅ Profitable' if profit_loss['profit_loss'] >= 0 else '⚠️ Operating at Loss'}
            """

            summary_label = ctk.CTkLabel(
                summary_frame,
                text=summary_text,
                font=ctk.CTkFont(size=14),
                justify="left"
            )
            summary_label.grid(row=0, column=0, padx=20, pady=20)

            # Recent transactions
            transactions = FinancialTransaction.get_all()
            if transactions:
                trans_text = "Recent Transactions:\n\n"
                for trans in transactions[-5:]:  # Last 5 transactions
                    trans_text += f"• {trans[1].title()}: ${trans[3]:.2f} - {trans[4] or 'N/A'}\n"

                trans_label = ctk.CTkLabel(
                    self.main_frame,
                    text=trans_text,
                    font=ctk.CTkFont(size=12),
                    justify="left"
                )
                trans_label.grid(row=2, column=0, padx=20, pady=10, sticky="w")

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.main_frame,
                text=f"Error loading financial data: {e}",
                text_color="red"
            )
            error_label.grid(row=1, column=0, padx=20, pady=10)

    def load_settings(self):
        """Load settings view"""
        self.clear_main_frame()

        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Settings",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        settings_frame = ctk.CTkFrame(self.main_frame)
        settings_frame.grid(row=1, column=0, padx=20, pady=20, sticky="ew")

        settings_text = f"""
⚙️ System Settings:

🏫 Academy Name: {app_settings.get_academy_name()}
💱 Currency: {app_settings.get_currency()}
🎨 Theme: {app_settings.get_theme_mode().title()}
🔧 Version: 1.0.0

📊 Database Status: Connected
💾 Backup Status: Available
🔐 Security: Active
        """

        settings_label = ctk.CTkLabel(
            settings_frame,
            text=settings_text,
            font=ctk.CTkFont(size=14),
            justify="left"
        )
        settings_label.grid(row=0, column=0, padx=20, pady=20)

    def logout(self):
        """Handle logout"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.app.logout()

    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            self.app.exit_application()

def main():
    """Main entry point"""
    print("=" * 60)
    print("🎓 Educational Academy Management System - Complete Version")
    print("=" * 60)
    print("Starting application...")

    try:
        app = AcademyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        messagebox.showerror("Fatal Error", f"Application failed to start: {e}")

if __name__ == "__main__":
    main()