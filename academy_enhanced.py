#!/usr/bin/env python3
"""
Educational Academy Management System - Enhanced UI Version
Improved design with better layout and styling
"""

import sys
import os
import customtkinter as ctk
import tkinter.messagebox as messagebox
from tkinter import ttk
import tkinter as tk

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from config.settings import app_settings
from models.user import User
from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.attendance import Attendance
from models.financial import FinancialTransaction

class AcademyApp:
    def __init__(self):
        """Initialize the Academy Management Application"""
        self.setup_customtkinter()
        self.current_user = None
        self.main_window = None
        self.login_window = None

        # Initialize database
        try:
            db_manager.init_database()
            print("✓ Database initialized successfully")
        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to initialize database: {e}")
            sys.exit(1)

        # Create login window
        self.show_login()

    def setup_customtkinter(self):
        """Setup CustomTkinter appearance and theme"""
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        ctk.set_widget_scaling(1.0)
        ctk.set_window_scaling(1.0)

    def show_login(self):
        """Show login window"""
        self.login_window = LoginWindow(self)

    def login_success(self, user):
        """Handle successful login"""
        try:
            self.current_user = user
            print(f"✓ User {user.username} logged in successfully")

            # Close login window safely
            if self.login_window:
                self.login_window.withdraw()
                self.login_window.quit()
                self.login_window = None

            # Open main application window
            self.show_main_window()
        except Exception as e:
            print(f"Error during login success: {e}")
            messagebox.showerror("Error", f"Failed to open main window: {e}")

    def show_main_window(self):
        """Show main application window"""
        try:
            self.main_window = MainWindow(self, self.current_user)
            self.main_window.mainloop()
        except Exception as e:
            print(f"Error showing main window: {e}")
            messagebox.showerror("Error", f"Main window error: {e}")

    def logout(self):
        """Handle user logout"""
        try:
            if self.main_window:
                self.main_window.quit()
                self.main_window = None

            self.current_user = None
            self.show_login()
        except Exception as e:
            print(f"Error during logout: {e}")
            self.exit_application()

    def exit_application(self):
        """Exit the application"""
        try:
            if self.main_window:
                self.main_window.quit()
            if self.login_window:
                self.login_window.quit()
            sys.exit(0)
        except:
            sys.exit(0)

    def run(self):
        """Start the application"""
        try:
            if self.login_window:
                self.login_window.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            self.exit_application()
        except Exception as e:
            print(f"Application error: {e}")
            self.exit_application()

class LoginWindow(ctk.CTk):
    def __init__(self, app):
        super().__init__()

        self.app = app
        self.setup_window()
        self.create_widgets()
        self.center_window()

        # Focus on username entry
        self.after(100, lambda: self.username_entry.focus())

    def setup_window(self):
        """Setup the login window"""
        self.title(f"{app_settings.get_academy_name()} - Login")
        self.geometry("500x650")
        self.resizable(False, False)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame with gradient-like effect
        main_frame = ctk.CTkFrame(self, corner_radius=15)
        main_frame.grid(row=0, column=0, padx=25, pady=25, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Header section
        header_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        header_frame.grid(row=0, column=0, padx=30, pady=(30, 20), sticky="ew")

        # Academy logo/title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎓 " + app_settings.get_academy_name(),
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#1f538d", "#4a9eff")
        )
        title_label.grid(row=0, column=0, pady=(0, 10))

        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Management System",
            font=ctk.CTkFont(size=18),
            text_color=("gray60", "gray40")
        )
        subtitle_label.grid(row=1, column=0)

        # Login form frame
        form_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        form_frame.grid(row=1, column=0, padx=30, pady=20, sticky="ew")
        form_frame.grid_columnconfigure(0, weight=1)

        # Username field
        username_label = ctk.CTkLabel(
            form_frame,
            text="👤 Username:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        username_label.grid(row=0, column=0, padx=25, pady=(25, 8), sticky="w")

        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your username",
            width=400,
            height=40,
            font=ctk.CTkFont(size=14),
            corner_radius=8
        )
        self.username_entry.grid(row=1, column=0, padx=25, pady=(0, 20), sticky="ew")
        self.username_entry.bind("<Return>", self.focus_password)

        # Password field
        password_label = ctk.CTkLabel(
            form_frame,
            text="🔒 Password:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        password_label.grid(row=2, column=0, padx=25, pady=(0, 8), sticky="w")

        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your password",
            show="*",
            width=400,
            height=40,
            font=ctk.CTkFont(size=14),
            corner_radius=8
        )
        self.password_entry.grid(row=3, column=0, padx=25, pady=(0, 25), sticky="ew")
        self.password_entry.bind("<Return>", self.login_enter)

        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text="🚀 Login",
            command=self.login,
            width=400,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=8,
            fg_color=("#1f538d", "#4a9eff"),
            hover_color=("#14375e", "#3a7dd8")
        )
        self.login_button.grid(row=4, column=0, padx=25, pady=(0, 25))

        # Quick login section
        quick_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        quick_frame.grid(row=2, column=0, padx=30, pady=(0, 20), sticky="ew")
        quick_frame.grid_columnconfigure((0, 1, 2), weight=1)

        quick_label = ctk.CTkLabel(
            quick_frame,
            text="⚡ Quick Login:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        quick_label.grid(row=0, column=0, columnspan=3, padx=20, pady=(20, 10))

        # Quick login buttons with better styling
        admin_btn = ctk.CTkButton(
            quick_frame,
            text="👑 Admin",
            command=lambda: self.quick_login("admin", "admin123"),
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#dc2626", "#ef4444"),
            hover_color=("#b91c1c", "#dc2626")
        )
        admin_btn.grid(row=1, column=0, padx=8, pady=(0, 20))

        staff_btn = ctk.CTkButton(
            quick_frame,
            text="👨‍💼 Staff",
            command=lambda: self.quick_login("staff1", "password123"),
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#059669", "#10b981"),
            hover_color=("#047857", "#059669")
        )
        staff_btn.grid(row=1, column=1, padx=8, pady=(0, 20))

        accountant_btn = ctk.CTkButton(
            quick_frame,
            text="💰 Accountant",
            command=lambda: self.quick_login("accountant1", "password123"),
            width=120,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color=("#7c3aed", "#8b5cf6"),
            hover_color=("#6d28d9", "#7c3aed")
        )
        accountant_btn.grid(row=1, column=2, padx=8, pady=(0, 20))

        # Status label
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=13),
            height=25
        )
        self.status_label.grid(row=3, column=0, padx=30, pady=(0, 10))

        # Info section
        info_frame = ctk.CTkFrame(main_frame, corner_radius=10, fg_color=("gray90", "gray20"))
        info_frame.grid(row=4, column=0, padx=30, pady=(0, 30), sticky="ew")

        info_label = ctk.CTkLabel(
            info_frame,
            text="📋 Available Accounts:",
            font=ctk.CTkFont(size=13, weight="bold")
        )
        info_label.grid(row=0, column=0, padx=20, pady=(15, 8))

        creds_text = """👑 Admin: admin / admin123 (Full Access)
👨‍💼 Staff: staff1 / password123 (Students, Teachers, Courses)
💰 Accountant: accountant1 / password123 (Financial Management)"""

        creds_label = ctk.CTkLabel(
            info_frame,
            text=creds_text,
            font=ctk.CTkFont(size=11),
            justify="left"
        )
        creds_label.grid(row=1, column=0, padx=20, pady=(0, 15))

    def quick_login(self, username, password):
        """Quick login with predefined credentials"""
        self.username_entry.delete(0, 'end')
        self.password_entry.delete(0, 'end')
        self.username_entry.insert(0, username)
        self.password_entry.insert(0, password)
        self.login()

    def focus_password(self, event=None):
        """Focus on password field"""
        self.password_entry.focus()

    def login_enter(self, event=None):
        """Handle Enter key in password field"""
        self.login()

    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()

        # Get window dimensions
        window_width = self.winfo_width()
        window_height = self.winfo_height()

        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # Calculate position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def login(self):
        """Handle login attempt"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get()

            # Validate input
            if not username:
                self.show_status("⚠️ Please enter a username", "error")
                self.username_entry.focus()
                return

            if not password:
                self.show_status("⚠️ Please enter a password", "error")
                self.password_entry.focus()
                return

            # Disable login button during authentication
            self.login_button.configure(state="disabled", text="🔄 Logging in...")
            self.show_status("🔍 Authenticating...", "info")
            self.update_idletasks()

            # Authenticate user
            user = User.get_by_username(username)

            if user and user.verify_password(password):
                self.show_status("✅ Login successful!", "success")
                self.after(500, lambda: self.app.login_success(user))
            else:
                self.show_status("❌ Invalid username or password", "error")
                self.password_entry.delete(0, 'end')
                self.password_entry.focus()
                self.login_button.configure(state="normal", text="🚀 Login")

        except Exception as e:
            self.show_status(f"❌ Login error: {str(e)}", "error")
            print(f"Login error: {e}")
            self.login_button.configure(state="normal", text="🚀 Login")

    def show_status(self, message, status_type="info"):
        """Show status message with appropriate color"""
        try:
            colors = {
                "info": ("gray60", "gray40"),
                "success": ("#059669", "#10b981"),
                "error": ("#dc2626", "#ef4444")
            }

            self.status_label.configure(
                text=message,
                text_color=colors.get(status_type, ("gray60", "gray40"))
            )
        except Exception as e:
            print(f"Error showing status: {e}")

    def on_closing(self):
        """Handle window closing"""
        self.app.exit_application()

class MainWindow(ctk.CTk):
    def __init__(self, app, user):
        super().__init__()

        self.app = app
        self.user = user
        self.current_view = None

        self.setup_window()
        self.create_widgets()
        self.load_dashboard()

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window(self):
        """Setup the main window"""
        self.title(f"{app_settings.get_academy_name()} - Management System")
        self.geometry("1400x900")
        self.minsize(1200, 700)

        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Enhanced sidebar
        self.create_enhanced_sidebar()

        # Main content area with better styling
        self.main_frame = ctk.CTkFrame(self, corner_radius=15)
        self.main_frame.grid(row=0, column=1, padx=(0, 15), pady=15, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

    def create_enhanced_sidebar(self):
        """Create enhanced sidebar with better styling"""
        self.sidebar = ctk.CTkFrame(self, width=320, corner_radius=15)
        self.sidebar.grid(row=0, column=0, padx=(15, 8), pady=15, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)

        # Header section
        header_frame = ctk.CTkFrame(self.sidebar, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=20, pady=(20, 15), sticky="ew")

        # Academy name with icon
        academy_label = ctk.CTkLabel(
            header_frame,
            text=f"🎓 {app_settings.get_academy_name()}",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#1f538d", "#4a9eff")
        )
        academy_label.grid(row=0, column=0, padx=20, pady=(15, 10))

        # User info section
        user_frame = ctk.CTkFrame(header_frame, corner_radius=8)
        user_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"👋 Welcome, {self.user.full_name}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        user_label.grid(row=0, column=0, padx=15, pady=(12, 5))

        # Role badge with color coding
        role_colors = {
            "admin": ("#dc2626", "#ef4444"),
            "staff": ("#059669", "#10b981"),
            "accountant": ("#7c3aed", "#8b5cf6")
        }
        role_color = role_colors.get(self.user.role, ("#6b7280", "#9ca3af"))

        role_label = ctk.CTkLabel(
            user_frame,
            text=f"🏷️ {self.user.role.title()}",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=role_color
        )
        role_label.grid(row=1, column=0, padx=15, pady=(0, 12))

        # Navigation section
        nav_frame = ctk.CTkFrame(self.sidebar, corner_radius=10)
        nav_frame.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="ew")

        nav_title = ctk.CTkLabel(
            nav_frame,
            text="📋 Navigation",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        nav_title.grid(row=0, column=0, padx=20, pady=(15, 10), sticky="w")

        # Navigation buttons with enhanced styling
        nav_row = 1

        # Dashboard
        dashboard_btn = self.create_nav_button(
            nav_frame, "📊 Dashboard", self.load_dashboard, nav_row,
            ("#1f538d", "#4a9eff")
        )
        nav_row += 1

        # Students (available to all roles)
        students_btn = self.create_nav_button(
            nav_frame, "👥 Students", self.load_students, nav_row,
            ("#059669", "#10b981")
        )
        nav_row += 1

        # Teachers (admin and staff only)
        if self.user.has_permission('teachers'):
            teachers_btn = self.create_nav_button(
                nav_frame, "👨‍🏫 Teachers", self.load_teachers, nav_row,
                ("#ea580c", "#f97316")
            )
            nav_row += 1

        # Courses (admin and staff only)
        if self.user.has_permission('courses'):
            courses_btn = self.create_nav_button(
                nav_frame, "📚 Courses", self.load_courses, nav_row,
                ("#7c3aed", "#8b5cf6")
            )
            nav_row += 1

        # Attendance (admin and staff only)
        if self.user.has_permission('attendance'):
            attendance_btn = self.create_nav_button(
                nav_frame, "✅ Attendance", self.load_attendance, nav_row,
                ("#0891b2", "#06b6d4")
            )
            nav_row += 1

        # Financial (admin and accountant only)
        if self.user.has_permission('financial'):
            financial_btn = self.create_nav_button(
                nav_frame, "💰 Financial", self.load_financial, nav_row,
                ("#dc2626", "#ef4444")
            )
            nav_row += 1

        # Settings (admin only)
        if self.user.has_permission('all'):
            settings_btn = self.create_nav_button(
                nav_frame, "⚙️ Settings", self.load_settings, nav_row,
                ("#6b7280", "#9ca3af")
            )
            nav_row += 1

        # Add some spacing
        spacer = ctk.CTkLabel(nav_frame, text="", height=10)
        spacer.grid(row=nav_row, column=0, pady=5)

        # Logout button
        logout_btn = ctk.CTkButton(
            self.sidebar,
            text="🚪 Logout",
            command=self.logout,
            width=280,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40"),
            text_color=("gray10", "gray90"),
            corner_radius=10
        )
        logout_btn.grid(row=21, column=0, padx=20, pady=(0, 20))

    def create_nav_button(self, parent, text, command, row, color):
        """Create a navigation button with consistent styling"""
        button = ctk.CTkButton(
            parent,
            text=text,
            command=command,
            width=260,
            height=40,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=color,
            hover_color=(color[0] if isinstance(color, tuple) else color,
                        color[1] if isinstance(color, tuple) else color),
            corner_radius=8,
            anchor="w"
        )
        button.grid(row=row, column=0, padx=20, pady=5, sticky="ew")
        return button

    def clear_main_frame(self):
        """Clear the main content area"""
        try:
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            self.current_view = None
        except Exception as e:
            print(f"Error clearing main frame: {e}")

    def load_dashboard(self):
        """Load enhanced dashboard view"""
        self.clear_main_frame()

        # Header section
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="📊 Dashboard Overview",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#1f538d", "#4a9eff")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Stats section with enhanced cards
        stats_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        stats_frame.grid(row=1, column=0, padx=25, pady=(0, 15), sticky="ew")
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        try:
            total_students = len(Student.get_all())
            total_teachers = len(Teacher.get_all())
            total_courses = len(Course.get_all())

            # Financial summary
            profit_loss = FinancialTransaction.get_profit_loss()

            # Create enhanced stat cards
            self.create_enhanced_stat_card(stats_frame, "Total Students", str(total_students), "👥", 0, ("#059669", "#10b981"))
            self.create_enhanced_stat_card(stats_frame, "Total Teachers", str(total_teachers), "👨‍🏫", 1, ("#ea580c", "#f97316"))
            self.create_enhanced_stat_card(stats_frame, "Total Courses", str(total_courses), "📚", 2, ("#7c3aed", "#8b5cf6"))
            self.create_enhanced_stat_card(stats_frame, "Monthly Profit", f"${profit_loss['profit_loss']:.2f}", "💰", 3, ("#dc2626", "#ef4444"))

        except Exception as e:
            error_label = ctk.CTkLabel(
                stats_frame,
                text=f"❌ Error loading statistics: {e}",
                text_color=("#dc2626", "#ef4444"),
                font=ctk.CTkFont(size=14)
            )
            error_label.grid(row=0, column=0, columnspan=4, padx=25, pady=25)

        # Quick actions section
        actions_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        actions_frame.grid(row=2, column=0, padx=25, pady=(0, 25), sticky="ew")
        actions_frame.grid_columnconfigure(0, weight=1)

        actions_title = ctk.CTkLabel(
            actions_frame,
            text="⚡ Quick Actions",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        actions_title.grid(row=0, column=0, padx=25, pady=(20, 15), sticky="w")

        # Enhanced quick action buttons
        buttons_frame = ctk.CTkFrame(actions_frame, corner_radius=8)
        buttons_frame.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1, 2), weight=1)

        if self.user.has_permission('students'):
            add_student_btn = ctk.CTkButton(
                buttons_frame,
                text="👥 Manage Students",
                command=self.load_students,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=("#059669", "#10b981"),
                hover_color=("#047857", "#059669"),
                corner_radius=8
            )
            add_student_btn.grid(row=0, column=0, padx=15, pady=15, sticky="ew")

        if self.user.has_permission('attendance'):
            record_attendance_btn = ctk.CTkButton(
                buttons_frame,
                text="✅ Record Attendance",
                command=self.load_attendance,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=("#0891b2", "#06b6d4"),
                hover_color=("#0e7490", "#0891b2"),
                corner_radius=8
            )
            record_attendance_btn.grid(row=0, column=1, padx=15, pady=15, sticky="ew")

        if self.user.has_permission('financial'):
            financial_btn = ctk.CTkButton(
                buttons_frame,
                text="💰 Financial Reports",
                command=self.load_financial,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=("#dc2626", "#ef4444"),
                hover_color=("#b91c1c", "#dc2626"),
                corner_radius=8
            )
            financial_btn.grid(row=0, column=2, padx=15, pady=15, sticky="ew")

    def create_enhanced_stat_card(self, parent, title, value, icon, column, color):
        """Create an enhanced statistics card"""
        card_frame = ctk.CTkFrame(parent, corner_radius=12)
        card_frame.grid(row=0, column=column, padx=15, pady=25, sticky="ew")

        # Icon with colored background
        icon_frame = ctk.CTkFrame(card_frame, corner_radius=8, fg_color=color)
        icon_frame.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")

        icon_label = ctk.CTkLabel(
            icon_frame,
            text=icon,
            font=ctk.CTkFont(size=28),
            text_color="white"
        )
        icon_label.grid(row=0, column=0, padx=15, pady=10)

        # Value
        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=color
        )
        value_label.grid(row=1, column=0, padx=20, pady=5)

        # Title
        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=("gray60", "gray40")
        )
        title_label.grid(row=2, column=0, padx=20, pady=(5, 20))

    def load_students(self):
        """Load enhanced students management view"""
        self.clear_main_frame()

        # Header section
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="👥 Student Management",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#059669", "#10b981")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Controls section
        controls_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        controls_frame.grid(row=1, column=0, padx=25, pady=(0, 15), sticky="ew")
        controls_frame.grid_columnconfigure(1, weight=1)

        # Search section
        search_label = ctk.CTkLabel(
            controls_frame,
            text="🔍 Search:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        search_label.grid(row=0, column=0, padx=(25, 15), pady=20)

        self.search_entry = ctk.CTkEntry(
            controls_frame,
            placeholder_text="Search by name or student ID...",
            height=35,
            font=ctk.CTkFont(size=13),
            corner_radius=8
        )
        self.search_entry.grid(row=0, column=1, padx=(0, 15), pady=20, sticky="ew")
        self.search_entry.bind("<KeyRelease>", self.on_search_change)

        search_btn = ctk.CTkButton(
            controls_frame,
            text="🔍 Search",
            command=self.search_students,
            width=100,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#0891b2", "#06b6d4"),
            hover_color=("#0e7490", "#0891b2"),
            corner_radius=8
        )
        search_btn.grid(row=0, column=2, padx=(0, 15), pady=20)

        add_btn = ctk.CTkButton(
            controls_frame,
            text="➕ Add Student",
            command=self.add_student,
            width=130,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color=("#059669", "#10b981"),
            hover_color=("#047857", "#059669"),
            corner_radius=8
        )
        add_btn.grid(row=0, column=3, padx=(0, 25), pady=20)

        # Students table section
        table_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        table_frame.grid(row=2, column=0, padx=25, pady=(0, 25), sticky="nsew")
        table_frame.grid_columnconfigure(0, weight=1)
        table_frame.grid_rowconfigure(1, weight=1)

        # Table header
        table_header = ctk.CTkLabel(
            table_frame,
            text="📋 Students List",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        table_header.grid(row=0, column=0, padx=25, pady=(20, 10), sticky="w")

        # Create enhanced treeview
        self.create_enhanced_students_table(table_frame)

        # Load students data
        self.load_students_data()

    def create_enhanced_students_table(self, parent):
        """Create enhanced students table with better styling"""
        # Create frame for table
        tree_container = ctk.CTkFrame(parent, corner_radius=8)
        tree_container.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="nsew")
        tree_container.grid_columnconfigure(0, weight=1)
        tree_container.grid_rowconfigure(0, weight=1)

        # Create style for treeview
        style = ttk.Style()
        style.theme_use("clam")

        # Configure treeview colors for dark theme
        style.configure("Treeview",
                       background="#2b2b2b",
                       foreground="white",
                       fieldbackground="#2b2b2b",
                       borderwidth=0,
                       font=("Segoe UI", 11))

        style.configure("Treeview.Heading",
                       background="#1f538d",
                       foreground="white",
                       borderwidth=1,
                       font=("Segoe UI", 11, "bold"))

        style.map("Treeview",
                 background=[('selected', '#4a9eff')],
                 foreground=[('selected', 'white')])

        # Create treeview with enhanced columns
        columns = ("ID", "Name", "Group", "Level", "Phone", "Address", "Status")
        self.students_tree = ttk.Treeview(
            tree_container,
            columns=columns,
            show="headings",
            height=15,
            style="Treeview"
        )

        # Configure column headings and widths
        column_configs = {
            "ID": ("Student ID", 100),
            "Name": ("Full Name", 200),
            "Group": ("Group", 150),
            "Level": ("Level", 80),
            "Phone": ("Parent Phone", 130),
            "Address": ("Address", 200),
            "Status": ("Status", 100)
        }

        for col, (heading, width) in column_configs.items():
            self.students_tree.heading(col, text=heading)
            self.students_tree.column(col, width=width, minwidth=width//2)

        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(tree_container, orient="vertical", command=self.students_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_container, orient="horizontal", command=self.students_tree.xview)

        self.students_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid treeview and scrollbars
        self.students_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # Bind events
        self.students_tree.bind("<Double-1>", self.edit_student)
        self.students_tree.bind("<Button-3>", self.show_context_menu)  # Right-click

    def load_students_data(self):
        """Load students data with enhanced display"""
        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # Get all students
            students = Student.get_all()

            # Populate treeview with enhanced data
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                # Determine status
                status = "Active" if student.is_active else "Inactive"

                # Insert with color coding
                item_id = self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A",
                    student.address or "N/A",
                    status
                ))

                # Color code based on status
                if not student.is_active:
                    self.students_tree.set(item_id, "Status", "⚠️ Inactive")
                else:
                    self.students_tree.set(item_id, "Status", "✅ Active")

            print(f"✓ Loaded {len(students)} students")

        except Exception as e:
            print(f"Error loading students: {e}")
            messagebox.showerror("Error", f"Failed to load students: {e}")

    def on_search_change(self, event=None):
        """Handle real-time search"""
        # Implement debounced search for better performance
        if hasattr(self, '_search_timer'):
            self.after_cancel(self._search_timer)
        self._search_timer = self.after(300, self.search_students)  # 300ms delay

    def search_students(self):
        """Enhanced search functionality"""
        search_term = self.search_entry.get().strip()

        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            if search_term:
                students = Student.search(search_term)
                search_status = f"Found {len(students)} students matching '{search_term}'"
            else:
                students = Student.get_all()
                search_status = f"Showing all {len(students)} students"

            # Populate treeview
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                # Determine status
                status = "✅ Active" if student.is_active else "⚠️ Inactive"

                self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A",
                    student.address or "N/A",
                    status
                ))

            print(search_status)

        except Exception as e:
            print(f"Search error: {e}")
            messagebox.showerror("Search Error", f"Search failed: {e}")

    def add_student(self):
        """Add new student - placeholder for future implementation"""
        messagebox.showinfo(
            "Add Student",
            "🚧 Add Student Feature\n\nThis feature will be implemented in the next version.\n\nIt will include:\n• Student registration form\n• Photo upload\n• Group assignment\n• Fee calculation"
        )

    def edit_student(self, event=None):
        """Edit selected student - placeholder for future implementation"""
        selection = self.students_tree.selection()
        if selection:
            item = self.students_tree.item(selection[0])
            student_id = item['values'][0]
            student_name = item['values'][1]

            messagebox.showinfo(
                "Edit Student",
                f"🚧 Edit Student Feature\n\nStudent: {student_name} (ID: {student_id})\n\nThis feature will be implemented in the next version.\n\nIt will include:\n• Edit personal information\n• Update contact details\n• Change group assignment\n• View attendance history"
            )

    def show_context_menu(self, event):
        """Show context menu for student operations"""
        # This would show a context menu with options like Edit, Delete, View Details
        pass

    def load_teachers(self):
        """Load enhanced teachers view"""
        self.clear_main_frame()

        # Header
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="👨‍🏫 Teachers Management",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#ea580c", "#f97316")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Content
        content_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        content_frame.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="nsew")

        try:
            teachers = Teacher.get_all()
            if teachers:
                # Create cards for each teacher
                for i, teacher in enumerate(teachers):
                    teacher_card = ctk.CTkFrame(content_frame, corner_radius=8)
                    teacher_card.grid(row=i//2, column=i%2, padx=20, pady=15, sticky="ew")

                    # Teacher info
                    name_label = ctk.CTkLabel(
                        teacher_card,
                        text=f"👨‍🏫 {teacher.full_name}",
                        font=ctk.CTkFont(size=16, weight="bold")
                    )
                    name_label.grid(row=0, column=0, padx=20, pady=(15, 5), sticky="w")

                    id_label = ctk.CTkLabel(
                        teacher_card,
                        text=f"ID: {teacher.teacher_id}",
                        font=ctk.CTkFont(size=12)
                    )
                    id_label.grid(row=1, column=0, padx=20, pady=2, sticky="w")

                    spec_label = ctk.CTkLabel(
                        teacher_card,
                        text=f"📚 Specialization: {teacher.specialization}",
                        font=ctk.CTkFont(size=12)
                    )
                    spec_label.grid(row=2, column=0, padx=20, pady=2, sticky="w")

                    salary_label = ctk.CTkLabel(
                        teacher_card,
                        text=f"💰 Salary: ${teacher.salary}/month",
                        font=ctk.CTkFont(size=12, weight="bold"),
                        text_color=("#059669", "#10b981")
                    )
                    salary_label.grid(row=3, column=0, padx=20, pady=(2, 15), sticky="w")

                content_frame.grid_columnconfigure((0, 1), weight=1)
            else:
                no_data_label = ctk.CTkLabel(
                    content_frame,
                    text="📝 No teachers found.\nRun demo.py to populate with sample data.",
                    font=ctk.CTkFont(size=14),
                    justify="center"
                )
                no_data_label.grid(row=0, column=0, padx=25, pady=50)
        except Exception as e:
            error_label = ctk.CTkLabel(
                content_frame,
                text=f"❌ Error loading teachers: {e}",
                text_color=("#dc2626", "#ef4444")
            )
            error_label.grid(row=0, column=0, padx=25, pady=50)

    def load_courses(self):
        """Load enhanced courses view"""
        self.clear_main_frame()

        # Header
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="📚 Courses Management",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#7c3aed", "#8b5cf6")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Content
        content_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        content_frame.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="nsew")

        try:
            courses = Course.get_all()
            if courses:
                # Create cards for each course
                for i, course in enumerate(courses):
                    course_card = ctk.CTkFrame(content_frame, corner_radius=8)
                    course_card.grid(row=i//2, column=i%2, padx=20, pady=15, sticky="ew")

                    # Course info
                    name_label = ctk.CTkLabel(
                        course_card,
                        text=f"📚 {course.name}",
                        font=ctk.CTkFont(size=16, weight="bold")
                    )
                    name_label.grid(row=0, column=0, padx=20, pady=(15, 5), sticky="w")

                    levels_label = ctk.CTkLabel(
                        course_card,
                        text=f"📊 Levels: {course.total_levels}",
                        font=ctk.CTkFont(size=12)
                    )
                    levels_label.grid(row=1, column=0, padx=20, pady=2, sticky="w")

                    sessions_label = ctk.CTkLabel(
                        course_card,
                        text=f"🕐 Sessions per level: {course.sessions_per_level}",
                        font=ctk.CTkFont(size=12)
                    )
                    sessions_label.grid(row=2, column=0, padx=20, pady=2, sticky="w")

                    fee_label = ctk.CTkLabel(
                        course_card,
                        text=f"💵 Fee per level: ${course.fee_per_level}",
                        font=ctk.CTkFont(size=12, weight="bold"),
                        text_color=("#059669", "#10b981")
                    )
                    fee_label.grid(row=3, column=0, padx=20, pady=(2, 15), sticky="w")

                content_frame.grid_columnconfigure((0, 1), weight=1)
            else:
                no_data_label = ctk.CTkLabel(
                    content_frame,
                    text="📝 No courses found.\nRun demo.py to populate with sample data.",
                    font=ctk.CTkFont(size=14),
                    justify="center"
                )
                no_data_label.grid(row=0, column=0, padx=25, pady=50)
        except Exception as e:
            error_label = ctk.CTkLabel(
                content_frame,
                text=f"❌ Error loading courses: {e}",
                text_color=("#dc2626", "#ef4444")
            )
            error_label.grid(row=0, column=0, padx=25, pady=50)

    def load_attendance(self):
        """Load enhanced attendance view"""
        self.clear_main_frame()

        # Header
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="✅ Attendance Management",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#0891b2", "#06b6d4")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Info section
        info_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        info_frame.grid(row=1, column=0, padx=25, pady=(0, 15), sticky="ew")

        info_text = """🎯 Attendance System Features:

✅ ID-based attendance marking
📊 Automatic absence tracking
📈 Detailed attendance reports
📱 Real-time attendance monitoring
📋 Monthly attendance summaries"""

        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=14),
            justify="left"
        )
        info_label.grid(row=0, column=0, padx=25, pady=25, sticky="w")

        # Attendance summary
        summary_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        summary_frame.grid(row=2, column=0, padx=25, pady=(0, 25), sticky="nsew")

        summary_title = ctk.CTkLabel(
            summary_frame,
            text="📊 Recent Attendance Summary",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        summary_title.grid(row=0, column=0, padx=25, pady=(20, 15), sticky="w")

        try:
            students = Student.get_all()
            if students:
                summary_text = ""
                for student in students[:5]:  # Show first 5 students
                    summary = student.get_attendance_summary()
                    summary_text += f"👤 {student.full_name}: {summary['attendance_rate']:.1f}% attendance\n"
                    summary_text += f"   📊 {summary['present_sessions']}/{summary['total_sessions']} sessions attended\n\n"

                summary_label = ctk.CTkLabel(
                    summary_frame,
                    text=summary_text,
                    font=ctk.CTkFont(size=12),
                    justify="left"
                )
                summary_label.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="w")
            else:
                no_data_label = ctk.CTkLabel(
                    summary_frame,
                    text="📝 No attendance data available.\nRun demo.py to populate with sample data.",
                    font=ctk.CTkFont(size=14),
                    justify="center"
                )
                no_data_label.grid(row=1, column=0, padx=25, pady=25)
        except Exception as e:
            error_label = ctk.CTkLabel(
                summary_frame,
                text=f"❌ Error loading attendance data: {e}",
                text_color=("#dc2626", "#ef4444")
            )
            error_label.grid(row=1, column=0, padx=25, pady=25)

    def load_financial(self):
        """Load enhanced financial view"""
        self.clear_main_frame()

        # Header
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="💰 Financial Management",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#dc2626", "#ef4444")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        try:
            profit_loss = FinancialTransaction.get_profit_loss()

            # Financial summary cards
            summary_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
            summary_frame.grid(row=1, column=0, padx=25, pady=(0, 15), sticky="ew")
            summary_frame.grid_columnconfigure((0, 1, 2), weight=1)

            # Income card
            income_card = ctk.CTkFrame(summary_frame, corner_radius=8)
            income_card.grid(row=0, column=0, padx=15, pady=25, sticky="ew")

            income_icon = ctk.CTkLabel(income_card, text="📈", font=ctk.CTkFont(size=24))
            income_icon.grid(row=0, column=0, padx=20, pady=(15, 5))

            income_value = ctk.CTkLabel(
                income_card,
                text=f"${profit_loss['income']:.2f}",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=("#059669", "#10b981")
            )
            income_value.grid(row=1, column=0, padx=20, pady=5)

            income_label = ctk.CTkLabel(income_card, text="Total Income", font=ctk.CTkFont(size=12))
            income_label.grid(row=2, column=0, padx=20, pady=(5, 15))

            # Expenses card
            expenses_card = ctk.CTkFrame(summary_frame, corner_radius=8)
            expenses_card.grid(row=0, column=1, padx=15, pady=25, sticky="ew")

            expenses_icon = ctk.CTkLabel(expenses_card, text="📉", font=ctk.CTkFont(size=24))
            expenses_icon.grid(row=0, column=0, padx=20, pady=(15, 5))

            expenses_value = ctk.CTkLabel(
                expenses_card,
                text=f"${profit_loss['expenses']:.2f}",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=("#dc2626", "#ef4444")
            )
            expenses_value.grid(row=1, column=0, padx=20, pady=5)

            expenses_label = ctk.CTkLabel(expenses_card, text="Total Expenses", font=ctk.CTkFont(size=12))
            expenses_label.grid(row=2, column=0, padx=20, pady=(5, 15))

            # Profit/Loss card
            profit_card = ctk.CTkFrame(summary_frame, corner_radius=8)
            profit_card.grid(row=0, column=2, padx=15, pady=25, sticky="ew")

            profit_icon = ctk.CTkLabel(profit_card, text="💵", font=ctk.CTkFont(size=24))
            profit_icon.grid(row=0, column=0, padx=20, pady=(15, 5))

            profit_color = ("#059669", "#10b981") if profit_loss['profit_loss'] >= 0 else ("#dc2626", "#ef4444")
            profit_value = ctk.CTkLabel(
                profit_card,
                text=f"${profit_loss['profit_loss']:.2f}",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=profit_color
            )
            profit_value.grid(row=1, column=0, padx=20, pady=5)

            profit_label = ctk.CTkLabel(profit_card, text="Net Profit/Loss", font=ctk.CTkFont(size=12))
            profit_label.grid(row=2, column=0, padx=20, pady=(5, 15))

            # Status indicator
            status_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
            status_frame.grid(row=2, column=0, padx=25, pady=(0, 25), sticky="ew")

            status_text = "✅ Academy is Profitable!" if profit_loss['profit_loss'] >= 0 else "⚠️ Academy is Operating at Loss"
            status_color = ("#059669", "#10b981") if profit_loss['profit_loss'] >= 0 else ("#dc2626", "#ef4444")

            status_label = ctk.CTkLabel(
                status_frame,
                text=status_text,
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=status_color
            )
            status_label.grid(row=0, column=0, padx=25, pady=25)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.main_frame,
                text=f"❌ Error loading financial data: {e}",
                text_color=("#dc2626", "#ef4444")
            )
            error_label.grid(row=1, column=0, padx=25, pady=50)

    def load_settings(self):
        """Load enhanced settings view"""
        self.clear_main_frame()

        # Header
        header_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        header_frame.grid(row=0, column=0, padx=25, pady=(25, 15), sticky="ew")

        title_label = ctk.CTkLabel(
            header_frame,
            text="⚙️ System Settings",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#6b7280", "#9ca3af")
        )
        title_label.grid(row=0, column=0, padx=25, pady=20, sticky="w")

        # Settings content
        settings_frame = ctk.CTkFrame(self.main_frame, corner_radius=10)
        settings_frame.grid(row=1, column=0, padx=25, pady=(0, 25), sticky="nsew")

        settings_text = f"""🏫 Academy Information:
• Name: {app_settings.get_academy_name()}
• Currency: {app_settings.get_currency()}
• Theme: {app_settings.get_theme_mode().title()}

🔧 System Information:
• Version: 1.0.0 Enhanced
• Database: SQLite (Connected)
• Security: Active
• Backup: Available

📊 Performance:
• Response Time: Optimized
• Memory Usage: Efficient
• UI Framework: CustomTkinter"""

        settings_label = ctk.CTkLabel(
            settings_frame,
            text=settings_text,
            font=ctk.CTkFont(size=14),
            justify="left"
        )
        settings_label.grid(row=0, column=0, padx=25, pady=25, sticky="w")

    def logout(self):
        """Handle logout with confirmation"""
        if messagebox.askyesno("Logout Confirmation", "🚪 Are you sure you want to logout?"):
            self.app.logout()

    def on_closing(self):
        """Handle window closing with confirmation"""
        if messagebox.askyesno("Exit Confirmation", "❌ Are you sure you want to exit the application?"):
            self.app.exit_application()

def main():
    """Main entry point"""
    print("=" * 70)
    print("🎓 Educational Academy Management System - Enhanced UI Version")
    print("=" * 70)
    print("Starting application...")

    try:
        app = AcademyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        messagebox.showerror("Fatal Error", f"Application failed to start: {e}")

if __name__ == "__main__":
    main()
