import customtkinter as ctk
import tkinter.messagebox as messagebox
from config.settings import app_settings
from models.student import Student
from models.teacher import Teacher
from models.course import Course
from models.group import Group
from models.financial import FinancialTransaction

class MainWindow(ctk.CTk):
    def __init__(self, app, user):
        super().__init__()

        self.app = app
        self.user = user
        self.current_view = None

        self.setup_window()
        self.create_widgets()
        self.load_dashboard()

        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window(self):
        """Setup the main window"""
        self.title(f"{app_settings.get_academy_name()} - Management System")
        self.geometry("1200x800")
        self.minsize(1000, 600)

        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create and arrange widgets"""
        # Sidebar
        self.create_sidebar()

        # Main content area
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=(0, 10), pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

    def create_sidebar(self):
        """Create the sidebar with navigation"""
        self.sidebar = ctk.CTkFrame(self, width=250, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)  # Spacer

        # Academy name
        academy_label = ctk.CTkLabel(
            self.sidebar,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        academy_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        # User info
        user_frame = ctk.CTkFrame(self.sidebar)
        user_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"Welcome, {self.user.full_name}",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        user_label.grid(row=0, column=0, padx=10, pady=(10, 5))

        role_label = ctk.CTkLabel(
            user_frame,
            text=f"Role: {self.user.role.title()}",
            font=ctk.CTkFont(size=10)
        )
        role_label.grid(row=1, column=0, padx=10, pady=(0, 10))

        # Navigation buttons
        nav_buttons = []

        # Dashboard
        dashboard_btn = ctk.CTkButton(
            self.sidebar,
            text="📊 Dashboard",
            command=self.load_dashboard,
            anchor="w",
            height=40
        )
        dashboard_btn.grid(row=2, column=0, padx=20, pady=(10, 5), sticky="ew")
        nav_buttons.append(dashboard_btn)

        # Students (available to all roles)
        students_btn = ctk.CTkButton(
            self.sidebar,
            text="👥 Students",
            command=self.load_students,
            anchor="w",
            height=40
        )
        students_btn.grid(row=3, column=0, padx=20, pady=5, sticky="ew")
        nav_buttons.append(students_btn)

        # Teachers (admin and staff only)
        if self.user.has_permission('teachers'):
            teachers_btn = ctk.CTkButton(
                self.sidebar,
                text="👨‍🏫 Teachers",
                command=self.load_teachers,
                anchor="w",
                height=40
            )
            teachers_btn.grid(row=4, column=0, padx=20, pady=5, sticky="ew")
            nav_buttons.append(teachers_btn)

        # Courses (admin and staff only)
        if self.user.has_permission('courses'):
            courses_btn = ctk.CTkButton(
                self.sidebar,
                text="📚 Courses",
                command=self.load_courses,
                anchor="w",
                height=40
            )
            courses_btn.grid(row=5, column=0, padx=20, pady=5, sticky="ew")
            nav_buttons.append(courses_btn)

        # Attendance (admin and staff only)
        if self.user.has_permission('attendance'):
            attendance_btn = ctk.CTkButton(
                self.sidebar,
                text="✅ Attendance",
                command=self.load_attendance,
                anchor="w",
                height=40
            )
            attendance_btn.grid(row=6, column=0, padx=20, pady=5, sticky="ew")
            nav_buttons.append(attendance_btn)

        # Financial (admin and accountant only)
        if self.user.has_permission('financial'):
            financial_btn = ctk.CTkButton(
                self.sidebar,
                text="💰 Financial",
                command=self.load_financial,
                anchor="w",
                height=40
            )
            financial_btn.grid(row=7, column=0, padx=20, pady=5, sticky="ew")
            nav_buttons.append(financial_btn)

        # Settings (admin only)
        if self.user.has_permission('all'):
            settings_btn = ctk.CTkButton(
                self.sidebar,
                text="⚙️ Settings",
                command=self.load_settings,
                anchor="w",
                height=40
            )
            settings_btn.grid(row=8, column=0, padx=20, pady=5, sticky="ew")
            nav_buttons.append(settings_btn)

        # Logout button
        logout_btn = ctk.CTkButton(
            self.sidebar,
            text="🚪 Logout",
            command=self.logout,
            anchor="w",
            height=40,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray80", "gray20")
        )
        logout_btn.grid(row=21, column=0, padx=20, pady=(10, 20), sticky="ew")

        self.nav_buttons = nav_buttons

    def clear_main_frame(self):
        """Clear the main content area"""
        try:
            for widget in self.main_frame.winfo_children():
                widget.destroy()
        except Exception as e:
            print(f"Error clearing main frame: {e}")

    def load_dashboard(self):
        """Load the dashboard view"""
        self.clear_main_frame()

        # Dashboard title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Dashboard",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Stats frame
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # Get statistics
        try:
            total_students = len(Student.get_all())
            total_teachers = len(Teacher.get_all())
            total_courses = len(Course.get_all())

            # Financial summary
            profit_loss = FinancialTransaction.get_profit_loss()

            # Create stat cards
            self.create_stat_card(stats_frame, "Total Students", str(total_students), "👥", 0)
            self.create_stat_card(stats_frame, "Total Teachers", str(total_teachers), "👨‍🏫", 1)
            self.create_stat_card(stats_frame, "Total Courses", str(total_courses), "📚", 2)
            self.create_stat_card(stats_frame, "Monthly Profit", f"${profit_loss['profit_loss']:.2f}", "💰", 3)

        except Exception as e:
            error_label = ctk.CTkLabel(
                stats_frame,
                text=f"Error loading statistics: {e}",
                text_color="red"
            )
            error_label.grid(row=0, column=0, columnspan=4, padx=20, pady=20)

        # Recent activity frame
        activity_frame = ctk.CTkFrame(self.main_frame)
        activity_frame.grid(row=2, column=0, padx=20, pady=10, sticky="ew")
        activity_frame.grid_columnconfigure(0, weight=1)

        activity_title = ctk.CTkLabel(
            activity_frame,
            text="Quick Actions",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        activity_title.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Quick action buttons
        actions_frame = ctk.CTkFrame(activity_frame)
        actions_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        actions_frame.grid_columnconfigure((0, 1, 2), weight=1)

        if self.user.has_permission('students'):
            add_student_btn = ctk.CTkButton(
                actions_frame,
                text="Add New Student",
                command=self.load_students,
                height=40
            )
            add_student_btn.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        if self.user.has_permission('attendance'):
            record_attendance_btn = ctk.CTkButton(
                actions_frame,
                text="Record Attendance",
                command=self.load_attendance,
                height=40
            )
            record_attendance_btn.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        if self.user.has_permission('financial'):
            financial_btn = ctk.CTkButton(
                actions_frame,
                text="Financial Management",
                command=self.load_financial,
                height=40
            )
            financial_btn.grid(row=0, column=2, padx=10, pady=10, sticky="ew")

    def create_stat_card(self, parent, title, value, icon, column):
        """Create a statistics card"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=0, column=column, padx=10, pady=20, sticky="ew")

        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=24)
        )
        icon_label.grid(row=0, column=0, padx=20, pady=(20, 5))

        value_label = ctk.CTkLabel(
            card_frame,
            text=value,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        value_label.grid(row=1, column=0, padx=20, pady=5)

        title_label = ctk.CTkLabel(
            card_frame,
            text=title,
            font=ctk.CTkFont(size=12)
        )
        title_label.grid(row=2, column=0, padx=20, pady=(5, 20))

    def load_students(self):
        """Load the students management view"""
        self.clear_main_frame()
        from views.student_management import StudentManagementView
        self.current_view = StudentManagementView(self.main_frame, self.user)

    def load_teachers(self):
        """Load the teachers management view"""
        self.clear_main_frame()
        # TODO: Implement teacher management view
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="Teachers Management - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)

    def load_courses(self):
        """Load the courses management view"""
        self.clear_main_frame()
        # TODO: Implement course management view
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="Courses Management - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)

    def load_attendance(self):
        """Load the attendance management view"""
        self.clear_main_frame()
        # TODO: Implement attendance management view
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="Attendance Management - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)

    def load_financial(self):
        """Load the financial management view"""
        self.clear_main_frame()
        # TODO: Implement financial management view
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="Financial Management - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)

    def load_settings(self):
        """Load the settings view"""
        self.clear_main_frame()
        # TODO: Implement settings view
        placeholder_label = ctk.CTkLabel(
            self.main_frame,
            text="Settings - Coming Soon",
            font=ctk.CTkFont(size=24)
        )
        placeholder_label.grid(row=0, column=0, padx=20, pady=20)

    def logout(self):
        """Handle logout"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.app.logout()

    def on_closing(self):
        """Handle window closing"""
        self.app.exit_application()
