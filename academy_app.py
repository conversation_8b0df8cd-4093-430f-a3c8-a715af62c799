#!/usr/bin/env python3
"""
Educational Academy Management System - Stable Version
Fixed version with improved error handling and memory management
"""

import sys
import os
import customtkinter as ctk
import tkinter.messagebox as messagebox

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import db_manager
from config.settings import app_settings
from models.user import User
from models.student import Student
from models.teacher import Teacher
from models.course import Course

class AcademyApp:
    def __init__(self):
        """Initialize the Academy Management Application"""
        self.setup_customtkinter()
        self.current_user = None
        self.main_window = None
        self.login_window = None
        
        # Initialize database
        try:
            db_manager.init_database()
            print("✓ Database initialized successfully")
        except Exception as e:
            messagebox.showerror("Database Error", f"Failed to initialize database: {e}")
            sys.exit(1)
        
        # Create login window
        self.show_login()
    
    def setup_customtkinter(self):
        """Setup CustomTkinter appearance and theme"""
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        ctk.set_widget_scaling(1.0)
        ctk.set_window_scaling(1.0)
    
    def show_login(self):
        """Show login window"""
        self.login_window = LoginWindow(self)
    
    def login_success(self, user):
        """Handle successful login"""
        try:
            self.current_user = user
            print(f"✓ User {user.username} logged in successfully")
            
            # Close login window safely
            if self.login_window:
                self.login_window.withdraw()
                self.login_window.quit()
                self.login_window = None
            
            # Open main application window
            self.show_main_window()
        except Exception as e:
            print(f"Error during login success: {e}")
            messagebox.showerror("Error", f"Failed to open main window: {e}")
    
    def show_main_window(self):
        """Show main application window"""
        try:
            self.main_window = MainWindow(self, self.current_user)
            self.main_window.mainloop()
        except Exception as e:
            print(f"Error showing main window: {e}")
            messagebox.showerror("Error", f"Main window error: {e}")
    
    def logout(self):
        """Handle user logout"""
        try:
            if self.main_window:
                self.main_window.quit()
                self.main_window = None
            
            self.current_user = None
            self.show_login()
        except Exception as e:
            print(f"Error during logout: {e}")
            self.exit_application()
    
    def exit_application(self):
        """Exit the application"""
        try:
            if self.main_window:
                self.main_window.quit()
            if self.login_window:
                self.login_window.quit()
            sys.exit(0)
        except:
            sys.exit(0)
    
    def run(self):
        """Start the application"""
        try:
            if self.login_window:
                self.login_window.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            self.exit_application()
        except Exception as e:
            print(f"Application error: {e}")
            self.exit_application()

class LoginWindow(ctk.CTk):
    def __init__(self, app):
        super().__init__()
        
        self.app = app
        self.setup_window()
        self.create_widgets()
        self.center_window()
        
        # Focus on username entry
        self.after(100, lambda: self.username_entry.focus())
    
    def setup_window(self):
        """Setup the login window"""
        self.title(f"{app_settings.get_academy_name()} - Login")
        self.geometry("400x500")
        self.resizable(False, False)
        
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """Create and arrange widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Academy logo/title
        title_label = ctk.CTkLabel(
            main_frame,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(30, 10))
        
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Management System",
            font=ctk.CTkFont(size=16)
        )
        subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 30))
        
        # Login form frame
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        form_frame.grid_columnconfigure(0, weight=1)
        
        # Username field
        username_label = ctk.CTkLabel(form_frame, text="Username:")
        username_label.grid(row=0, column=0, padx=20, pady=(20, 5), sticky="w")
        
        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your username",
            width=300
        )
        self.username_entry.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="ew")
        self.username_entry.bind("<Return>", self.focus_password)
        
        # Password field
        password_label = ctk.CTkLabel(form_frame, text="Password:")
        password_label.grid(row=2, column=0, padx=20, pady=(0, 5), sticky="w")
        
        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter your password",
            show="*",
            width=300
        )
        self.password_entry.grid(row=3, column=0, padx=20, pady=(0, 20), sticky="ew")
        self.password_entry.bind("<Return>", self.login_enter)
        
        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text="Login",
            command=self.login,
            width=300,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.login_button.grid(row=4, column=0, padx=20, pady=(0, 20))
        
        # Status label
        self.status_label = ctk.CTkLabel(
            main_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.grid(row=3, column=0, padx=20, pady=10)
        
        # Default credentials info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.grid(row=4, column=0, padx=20, pady=(10, 20), sticky="ew")
        
        info_label = ctk.CTkLabel(
            info_frame,
            text="Default Login Credentials:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        info_label.grid(row=0, column=0, padx=20, pady=(15, 5))
        
        creds_label = ctk.CTkLabel(
            info_frame,
            text="Username: admin\nPassword: admin123",
            font=ctk.CTkFont(size=11)
        )
        creds_label.grid(row=1, column=0, padx=20, pady=(0, 15))
    
    def focus_password(self, event=None):
        """Focus on password field"""
        self.password_entry.focus()
    
    def login_enter(self, event=None):
        """Handle Enter key in password field"""
        self.login()
    
    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()
        
        # Get window dimensions
        window_width = self.winfo_width()
        window_height = self.winfo_height()
        
        # Get screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # Calculate position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def login(self):
        """Handle login attempt"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get()
            
            # Validate input
            if not username:
                self.show_status("Please enter a username", "error")
                self.username_entry.focus()
                return
            
            if not password:
                self.show_status("Please enter a password", "error")
                self.password_entry.focus()
                return
            
            # Disable login button during authentication
            self.login_button.configure(state="disabled", text="Logging in...")
            self.show_status("Authenticating...", "info")
            self.update_idletasks()
            
            # Authenticate user
            user = User.get_by_username(username)
            
            if user and user.verify_password(password):
                self.show_status("Login successful!", "success")
                self.after(500, lambda: self.app.login_success(user))
            else:
                self.show_status("Invalid username or password", "error")
                self.password_entry.delete(0, 'end')
                self.password_entry.focus()
                self.login_button.configure(state="normal", text="Login")
        
        except Exception as e:
            self.show_status(f"Login error: {str(e)}", "error")
            print(f"Login error: {e}")
            self.login_button.configure(state="normal", text="Login")
    
    def show_status(self, message, status_type="info"):
        """Show status message with appropriate color"""
        try:
            colors = {
                "info": "gray",
                "success": "green",
                "error": "red"
            }
            
            self.status_label.configure(
                text=message,
                text_color=colors.get(status_type, "gray")
            )
        except Exception as e:
            print(f"Error showing status: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        self.app.exit_application()

class MainWindow(ctk.CTk):
    def __init__(self, app, user):
        super().__init__()
        
        self.app = app
        self.user = user
        
        self.setup_window()
        self.create_widgets()
        self.load_dashboard()
        
        # Handle window closing
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_window(self):
        """Setup the main window"""
        self.title(f"{app_settings.get_academy_name()} - Management System")
        self.geometry("1000x700")
        self.minsize(800, 600)
        
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create and arrange widgets"""
        # Sidebar
        self.create_sidebar()
        
        # Main content area
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=(0, 10), pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
    
    def create_sidebar(self):
        """Create the sidebar with navigation"""
        self.sidebar = ctk.CTkFrame(self, width=250, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)
        
        # Academy name
        academy_label = ctk.CTkLabel(
            self.sidebar,
            text=app_settings.get_academy_name(),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        academy_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # User info
        user_frame = ctk.CTkFrame(self.sidebar)
        user_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        
        user_label = ctk.CTkLabel(
            user_frame,
            text=f"Welcome, {self.user.full_name}",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        user_label.grid(row=0, column=0, padx=10, pady=(10, 5))
        
        role_label = ctk.CTkLabel(
            user_frame,
            text=f"Role: {self.user.role.title()}",
            font=ctk.CTkFont(size=10)
        )
        role_label.grid(row=1, column=0, padx=10, pady=(0, 10))
        
        # Navigation buttons
        dashboard_btn = ctk.CTkButton(
            self.sidebar,
            text="📊 Dashboard",
            command=self.load_dashboard,
            anchor="w",
            height=40
        )
        dashboard_btn.grid(row=2, column=0, padx=20, pady=(10, 5), sticky="ew")
        
        students_btn = ctk.CTkButton(
            self.sidebar,
            text="👥 Students",
            command=self.load_students,
            anchor="w",
            height=40
        )
        students_btn.grid(row=3, column=0, padx=20, pady=5, sticky="ew")
        
        # Logout button
        logout_btn = ctk.CTkButton(
            self.sidebar,
            text="🚪 Logout",
            command=self.logout,
            anchor="w",
            height=40,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray80", "gray20")
        )
        logout_btn.grid(row=21, column=0, padx=20, pady=(10, 20), sticky="ew")
    
    def clear_main_frame(self):
        """Clear the main content area"""
        try:
            for widget in self.main_frame.winfo_children():
                widget.destroy()
        except Exception as e:
            print(f"Error clearing main frame: {e}")
    
    def load_dashboard(self):
        """Load the dashboard view"""
        self.clear_main_frame()
        
        # Dashboard title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Dashboard",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")
        
        # Stats frame
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        
        try:
            total_students = len(Student.get_all())
            total_teachers = len(Teacher.get_all())
            total_courses = len(Course.get_all())
            
            stats_text = f"""
📊 Academy Statistics:

👥 Total Students: {total_students}
👨‍🏫 Total Teachers: {total_teachers}
📚 Total Courses: {total_courses}

✅ System Status: All systems operational
🔐 Logged in as: {self.user.full_name} ({self.user.role.title()})
            """
            
            stats_label = ctk.CTkLabel(
                stats_frame,
                text=stats_text,
                font=ctk.CTkFont(size=14),
                justify="left"
            )
            stats_label.grid(row=0, column=0, padx=20, pady=20)
            
        except Exception as e:
            error_label = ctk.CTkLabel(
                stats_frame,
                text=f"Error loading statistics: {e}",
                text_color="red"
            )
            error_label.grid(row=0, column=0, padx=20, pady=20)
    
    def load_students(self):
        """Load the students view"""
        self.clear_main_frame()
        
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Student Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")
        
        info_label = ctk.CTkLabel(
            self.main_frame,
            text="Student management functionality is available!\nThis is a simplified view. Full interface is in development.",
            font=ctk.CTkFont(size=14)
        )
        info_label.grid(row=1, column=0, padx=20, pady=20)
        
        # Show some students
        try:
            students = Student.get_all()
            if students:
                students_text = "Current Students:\n\n"
                for i, student in enumerate(students[:10], 1):  # Show first 10
                    students_text += f"{i}. {student.full_name} ({student.student_id})\n"
                
                students_label = ctk.CTkLabel(
                    self.main_frame,
                    text=students_text,
                    font=ctk.CTkFont(size=12),
                    justify="left"
                )
                students_label.grid(row=2, column=0, padx=20, pady=10, sticky="w")
            else:
                no_students_label = ctk.CTkLabel(
                    self.main_frame,
                    text="No students found. Run demo.py to populate with sample data.",
                    font=ctk.CTkFont(size=12)
                )
                no_students_label.grid(row=2, column=0, padx=20, pady=10)
        except Exception as e:
            error_label = ctk.CTkLabel(
                self.main_frame,
                text=f"Error loading students: {e}",
                text_color="red"
            )
            error_label.grid(row=2, column=0, padx=20, pady=10)
    
    def logout(self):
        """Handle logout"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            self.app.logout()
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            self.app.exit_application()

def main():
    """Main entry point"""
    print("=" * 60)
    print("🎓 Educational Academy Management System")
    print("=" * 60)
    print("Starting application...")
    
    try:
        app = AcademyApp()
        app.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        messagebox.showerror("Fatal Error", f"Application failed to start: {e}")

if __name__ == "__main__":
    main()
