import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import os
from models.student import Student
from models.group import Group
from models.course import Course
from utils.validators import Validators

class StudentManagementView:
    def __init__(self, parent, user):
        self.parent = parent
        self.user = user
        self.selected_student = None
        self.students_data = []

        self.create_widgets()
        self.load_students()

    def create_widgets(self):
        """Create and arrange widgets"""
        # Configure parent grid
        self.parent.grid_columnconfigure(0, weight=1)
        self.parent.grid_rowconfigure(1, weight=1)

        # Title and controls frame
        header_frame = ctk.CTkFrame(self.parent)
        header_frame.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")
        header_frame.grid_columnconfigure(1, weight=1)

        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="Student Management",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")

        # Search frame
        search_frame = ctk.CTkFrame(header_frame)
        search_frame.grid(row=0, column=1, padx=20, pady=20, sticky="ew")
        search_frame.grid_columnconfigure(1, weight=1)

        search_label = ctk.CTkLabel(search_frame, text="Search:")
        search_label.grid(row=0, column=0, padx=(20, 10), pady=20)

        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Search by name or student ID..."
        )
        self.search_entry.grid(row=0, column=1, padx=(0, 10), pady=20, sticky="ew")
        self.search_entry.bind("<KeyRelease>", self.on_search)

        search_btn = ctk.CTkButton(
            search_frame,
            text="Search",
            command=self.search_students,
            width=80
        )
        search_btn.grid(row=0, column=2, padx=(0, 20), pady=20)

        # Add student button
        add_btn = ctk.CTkButton(
            header_frame,
            text="+ Add Student",
            command=self.show_add_student_dialog,
            width=120
        )
        add_btn.grid(row=0, column=2, padx=(0, 20), pady=20)

        # Main content frame
        content_frame = ctk.CTkFrame(self.parent)
        content_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        content_frame.grid_columnconfigure(0, weight=2)
        content_frame.grid_columnconfigure(1, weight=1)
        content_frame.grid_rowconfigure(0, weight=1)

        # Students list frame
        list_frame = ctk.CTkFrame(content_frame)
        list_frame.grid(row=0, column=0, padx=(20, 10), pady=20, sticky="nsew")
        list_frame.grid_columnconfigure(0, weight=1)
        list_frame.grid_rowconfigure(1, weight=1)

        list_title = ctk.CTkLabel(
            list_frame,
            text="Students List",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        list_title.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Create treeview for students list
        self.create_students_treeview(list_frame)

        # Student details frame
        details_frame = ctk.CTkFrame(content_frame)
        details_frame.grid(row=0, column=1, padx=(10, 20), pady=20, sticky="nsew")
        details_frame.grid_columnconfigure(0, weight=1)

        details_title = ctk.CTkLabel(
            details_frame,
            text="Student Details",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        details_title.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="w")

        # Student details content
        self.create_student_details(details_frame)

    def create_students_treeview(self, parent):
        """Create the students treeview"""
        # Treeview frame
        tree_frame = ctk.CTkFrame(parent)
        tree_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        tree_frame.grid_columnconfigure(0, weight=1)
        tree_frame.grid_rowconfigure(0, weight=1)

        # Create treeview
        columns = ("ID", "Name", "Group", "Level", "Phone")
        self.students_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

        # Configure columns
        self.students_tree.heading("ID", text="Student ID")
        self.students_tree.heading("Name", text="Full Name")
        self.students_tree.heading("Group", text="Group")
        self.students_tree.heading("Level", text="Level")
        self.students_tree.heading("Phone", text="Parent Phone")

        self.students_tree.column("ID", width=100)
        self.students_tree.column("Name", width=200)
        self.students_tree.column("Group", width=150)
        self.students_tree.column("Level", width=80)
        self.students_tree.column("Phone", width=120)

        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar.set)

        # Grid treeview and scrollbar
        self.students_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

        # Bind selection event
        self.students_tree.bind("<<TreeviewSelect>>", self.on_student_select)

        # Context menu
        self.create_context_menu()

    def create_context_menu(self):
        """Create context menu for treeview"""
        self.context_menu = tk.Menu(self.students_tree, tearoff=0)
        self.context_menu.add_command(label="Edit Student", command=self.edit_selected_student)
        self.context_menu.add_command(label="View Details", command=self.view_student_details)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Transfer to Group", command=self.transfer_student)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Delete Student", command=self.delete_selected_student)

        # Bind right-click
        self.students_tree.bind("<Button-3>", self.show_context_menu)

    def create_student_details(self, parent):
        """Create student details panel"""
        # Scrollable frame for details
        self.details_scrollable = ctk.CTkScrollableFrame(parent)
        self.details_scrollable.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        self.details_scrollable.grid_columnconfigure(0, weight=1)

        # Placeholder text
        self.no_selection_label = ctk.CTkLabel(
            self.details_scrollable,
            text="Select a student to view details",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.no_selection_label.grid(row=0, column=0, padx=20, pady=50)

    def load_students(self):
        """Load students into the treeview"""
        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # Get all students
            students = Student.get_all()
            self.students_data = students

            # Populate treeview
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A"
                ))

            # Update status
            self.update_status(f"Loaded {len(students)} students")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load students: {e}")

    def on_search(self, event=None):
        """Handle search input"""
        search_term = self.search_entry.get().strip()
        if len(search_term) >= 2:  # Start searching after 2 characters
            self.search_students()
        elif len(search_term) == 0:  # Show all when search is cleared
            self.load_students()

    def search_students(self):
        """Search students"""
        search_term = self.search_entry.get().strip()

        if not search_term:
            self.load_students()
            return

        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # Search students
            students = Student.search(search_term)

            # Populate treeview
            for student in students:
                # Get group name
                group_name = "No Group"
                if student.current_group_id:
                    group = Group.get_by_id(student.current_group_id)
                    if group:
                        group_name = group.name

                self.students_tree.insert("", "end", values=(
                    student.student_id,
                    student.full_name,
                    group_name,
                    student.current_level,
                    student.parent_phone or "N/A"
                ))

            # Update status
            self.update_status(f"Found {len(students)} students matching '{search_term}'")

        except Exception as e:
            messagebox.showerror("Error", f"Search failed: {e}")

    def on_student_select(self, event):
        """Handle student selection"""
        selection = self.students_tree.selection()
        if selection:
            item = self.students_tree.item(selection[0])
            student_id = item['values'][0]

            # Get student details
            self.selected_student = Student.get_by_student_id(student_id)
            self.display_student_details()

    def display_student_details(self):
        """Display selected student details"""
        # Clear existing details
        try:
            for widget in self.details_scrollable.winfo_children():
                widget.destroy()
        except Exception as e:
            print(f"Error clearing details: {e}")

        if not self.selected_student:
            self.no_selection_label = ctk.CTkLabel(
                self.details_scrollable,
                text="Select a student to view details",
                font=ctk.CTkFont(size=14),
                text_color="gray"
            )
            self.no_selection_label.grid(row=0, column=0, padx=20, pady=50)
            return

        student = self.selected_student

        # Student photo placeholder
        photo_frame = ctk.CTkFrame(self.details_scrollable)
        photo_frame.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")

        photo_label = ctk.CTkLabel(
            photo_frame,
            text="📷",
            font=ctk.CTkFont(size=48)
        )
        photo_label.grid(row=0, column=0, padx=20, pady=20)

        # Student information
        info_frame = ctk.CTkFrame(self.details_scrollable)
        info_frame.grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        info_frame.grid_columnconfigure(1, weight=1)

        # Basic info
        self.add_detail_row(info_frame, "Student ID:", student.student_id, 0)
        self.add_detail_row(info_frame, "Full Name:", student.full_name, 1)
        self.add_detail_row(info_frame, "Parent Phone:", student.parent_phone or "N/A", 2)
        self.add_detail_row(info_frame, "Address:", student.address or "N/A", 3)
        self.add_detail_row(info_frame, "Current Level:", str(student.current_level), 4)

        # Group info
        group_name = "No Group Assigned"
        if student.current_group_id:
            group = Group.get_by_id(student.current_group_id)
            if group:
                group_name = group.name

        self.add_detail_row(info_frame, "Current Group:", group_name, 5)

        # Action buttons
        buttons_frame = ctk.CTkFrame(self.details_scrollable)
        buttons_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1), weight=1)

        edit_btn = ctk.CTkButton(
            buttons_frame,
            text="Edit Student",
            command=self.edit_selected_student
        )
        edit_btn.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        transfer_btn = ctk.CTkButton(
            buttons_frame,
            text="Transfer Group",
            command=self.transfer_student
        )
        transfer_btn.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

    def add_detail_row(self, parent, label_text, value_text, row):
        """Add a detail row to the info frame"""
        label = ctk.CTkLabel(
            parent,
            text=label_text,
            font=ctk.CTkFont(weight="bold")
        )
        label.grid(row=row, column=0, padx=(20, 10), pady=5, sticky="w")

        value = ctk.CTkLabel(parent, text=str(value_text))
        value.grid(row=row, column=1, padx=(0, 20), pady=5, sticky="w")

    def show_context_menu(self, event):
        """Show context menu"""
        # Select the item under cursor
        item = self.students_tree.identify_row(event.y)
        if item:
            self.students_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def show_add_student_dialog(self):
        """Show add student dialog"""
        dialog = StudentDialog(self.parent, "Add Student", self.user)
        if dialog.result:
            self.load_students()

    def edit_selected_student(self):
        """Edit selected student"""
        if not self.selected_student:
            messagebox.showwarning("Warning", "Please select a student to edit")
            return

        dialog = StudentDialog(self.parent, "Edit Student", self.user, self.selected_student)
        if dialog.result:
            self.load_students()
            self.display_student_details()

    def view_student_details(self):
        """View student details (same as selection)"""
        pass  # Already handled by selection

    def transfer_student(self):
        """Transfer student to another group"""
        if not self.selected_student:
            messagebox.showwarning("Warning", "Please select a student to transfer")
            return

        # TODO: Implement transfer dialog
        messagebox.showinfo("Info", "Transfer functionality will be implemented soon")

    def delete_selected_student(self):
        """Delete selected student"""
        if not self.selected_student:
            messagebox.showwarning("Warning", "Please select a student to delete")
            return

        if messagebox.askyesno(
            "Confirm Delete",
            f"Are you sure you want to delete student '{self.selected_student.full_name}'?\n\nThis action cannot be undone."
        ):
            try:
                self.selected_student.deactivate()
                self.load_students()
                self.selected_student = None
                self.display_student_details()
                messagebox.showinfo("Success", "Student deleted successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete student: {e}")

    def update_status(self, message):
        """Update status message"""
        # TODO: Implement status bar
        print(f"Status: {message}")


class StudentDialog(ctk.CTkToplevel):
    def __init__(self, parent, title, user, student=None):
        super().__init__(parent)

        self.user = user
        self.student = student  # None for add, Student object for edit
        self.result = None

        self.setup_dialog(title)
        self.create_widgets()

        if student:
            self.populate_fields()

        # Make dialog modal
        self.transient(parent)
        self.grab_set()

        # Center dialog
        self.center_dialog()

        # Focus on first field
        self.student_id_entry.focus()

    def setup_dialog(self, title):
        """Setup dialog window"""
        self.title(title)
        self.geometry("500x600")
        self.resizable(False, False)

        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkScrollableFrame(self)
        main_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        main_frame.grid_columnconfigure(0, weight=1)

        # Student ID
        self.add_field(main_frame, "Student ID:", "student_id_entry", 0, required=True)

        # Full Name
        self.add_field(main_frame, "Full Name:", "full_name_entry", 1, required=True)

        # Parent Phone
        self.add_field(main_frame, "Parent Phone:", "parent_phone_entry", 2)

        # Address
        self.add_field(main_frame, "Address:", "address_entry", 3, multiline=True)

        # Group selection
        group_label = ctk.CTkLabel(main_frame, text="Group:")
        group_label.grid(row=4, column=0, padx=20, pady=(20, 5), sticky="w")

        self.group_var = ctk.StringVar()
        self.group_combobox = ctk.CTkComboBox(
            main_frame,
            variable=self.group_var,
            values=self.get_group_options(),
            state="readonly"
        )
        self.group_combobox.grid(row=5, column=0, padx=20, pady=(0, 15), sticky="ew")

        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.grid(row=6, column=0, padx=20, pady=20, sticky="ew")
        buttons_frame.grid_columnconfigure((0, 1), weight=1)

        save_btn = ctk.CTkButton(
            buttons_frame,
            text="Save",
            command=self.save_student
        )
        save_btn.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="Cancel",
            command=self.cancel,
            fg_color="gray"
        )
        cancel_btn.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

    def add_field(self, parent, label_text, entry_name, row, required=False, multiline=False):
        """Add a form field"""
        label = ctk.CTkLabel(
            parent,
            text=label_text + (" *" if required else "")
        )
        label.grid(row=row*2, column=0, padx=20, pady=(20, 5), sticky="w")

        if multiline:
            entry = ctk.CTkTextbox(parent, height=80)
        else:
            entry = ctk.CTkEntry(parent)

        entry.grid(row=row*2+1, column=0, padx=20, pady=(0, 15), sticky="ew")
        setattr(self, entry_name, entry)

    def get_group_options(self):
        """Get available groups for selection"""
        try:
            groups = Group.get_all()
            options = ["No Group"]

            for group_data in groups:
                group_name = group_data[1]  # group name
                course_name = group_data[6] if len(group_data) > 6 else "Unknown"  # course name
                options.append(f"{group_name} ({course_name})")

            return options
        except Exception as e:
            print(f"Error loading groups: {e}")
            return ["No Group"]

    def populate_fields(self):
        """Populate fields for editing"""
        if not self.student:
            return

        self.student_id_entry.insert(0, self.student.student_id)
        self.full_name_entry.insert(0, self.student.full_name)

        if self.student.parent_phone:
            self.parent_phone_entry.insert(0, self.student.parent_phone)

        if self.student.address:
            self.address_entry.insert("1.0", self.student.address)

        # Set group selection
        if self.student.current_group_id:
            group = Group.get_by_id(self.student.current_group_id)
            if group:
                # Find matching option in combobox
                for option in self.group_combobox.cget("values"):
                    if option.startswith(group.name):
                        self.group_var.set(option)
                        break

    def save_student(self):
        """Save student data"""
        try:
            # Validate input
            student_id = self.student_id_entry.get().strip()
            full_name = self.full_name_entry.get().strip()
            parent_phone = self.parent_phone_entry.get().strip()
            address = self.address_entry.get("1.0", "end-1c").strip()

            # Validation
            is_valid, message = Validators.validate_student_id(student_id)
            if not is_valid:
                messagebox.showerror("Validation Error", message)
                return

            is_valid, message = Validators.validate_name(full_name, "Full Name")
            if not is_valid:
                messagebox.showerror("Validation Error", message)
                return

            if parent_phone and not Validators.validate_phone(parent_phone):
                messagebox.showerror("Validation Error", "Invalid phone number format")
                return

            # Get selected group
            group_id = None
            group_selection = self.group_var.get()
            if group_selection and group_selection != "No Group":
                # Extract group name from selection
                group_name = group_selection.split(" (")[0]
                groups = Group.get_all()
                for group_data in groups:
                    if group_data[1] == group_name:  # group name
                        group_id = group_data[0]  # group id
                        break

            # Save student
            if self.student:  # Edit existing
                # Check if student ID changed and if it's unique
                if student_id != self.student.student_id:
                    existing = Student.get_by_student_id(student_id)
                    if existing:
                        messagebox.showerror("Error", "Student ID already exists")
                        return

                self.student.update(
                    student_id=student_id,
                    full_name=full_name,
                    parent_phone=parent_phone if parent_phone else None,
                    address=address if address else None,
                    current_group_id=group_id
                )
                messagebox.showinfo("Success", "Student updated successfully")
            else:  # Add new
                # Check if student ID is unique
                existing = Student.get_by_student_id(student_id)
                if existing:
                    messagebox.showerror("Error", "Student ID already exists")
                    return

                Student.create(
                    student_id=student_id,
                    full_name=full_name,
                    parent_phone=parent_phone if parent_phone else None,
                    address=address if address else None,
                    current_group_id=group_id
                )
                messagebox.showinfo("Success", "Student added successfully")

            self.result = True
            self.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save student: {e}")

    def cancel(self):
        """Cancel dialog"""
        self.result = False
        self.destroy()

    def center_dialog(self):
        """Center dialog on parent"""
        self.update_idletasks()

        # Get dialog dimensions
        dialog_width = self.winfo_width()
        dialog_height = self.winfo_height()

        # Get parent position and size
        parent_x = self.master.winfo_x()
        parent_y = self.master.winfo_y()
        parent_width = self.master.winfo_width()
        parent_height = self.master.winfo_height()

        # Calculate position
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
