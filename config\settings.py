import os
from config.database import db_manager

class Settings:
    def __init__(self):
        self.default_settings = {
            'academy_name': 'Educational Academy',
            'academy_logo': '',
            'currency': 'USD',
            'theme_mode': 'dark',
            'primary_color': '#1f538d',
            'secondary_color': '#14375e',
            'backup_frequency': 'weekly',
            'session_timeout': '30',
            'default_sessions_per_level': '10',
            'default_max_students_per_group': '20'
        }
        self.load_settings()
    
    def load_settings(self):
        """Load settings from database"""
        try:
            settings_data = db_manager.execute_query("SELECT key, value FROM settings")
            self.settings = dict(settings_data) if settings_data else {}
            
            # Add default settings if they don't exist
            for key, value in self.default_settings.items():
                if key not in self.settings:
                    self.settings[key] = value
                    self.save_setting(key, value)
        except Exception as e:
            print(f"Error loading settings: {e}")
            self.settings = self.default_settings.copy()
    
    def get(self, key, default=None):
        """Get a setting value"""
        return self.settings.get(key, default)
    
    def set(self, key, value):
        """Set a setting value"""
        self.settings[key] = value
        self.save_setting(key, value)
    
    def save_setting(self, key, value):
        """Save a setting to database"""
        try:
            db_manager.execute_update(
                "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)",
                (key, str(value))
            )
        except Exception as e:
            print(f"Error saving setting {key}: {e}")
    
    def get_academy_name(self):
        return self.get('academy_name')
    
    def get_currency(self):
        return self.get('currency')
    
    def get_theme_mode(self):
        return self.get('theme_mode')
    
    def get_primary_color(self):
        return self.get('primary_color')
    
    def get_secondary_color(self):
        return self.get('secondary_color')

# Global settings instance
app_settings = Settings()
